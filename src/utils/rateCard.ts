import loggerService from './logger/logger.service';
import RateCardSchema from '../models/rate-card.model';
import { IRateCard } from '../types';

interface RateCardQueryParams {
  packageIds: string[];
  startDateTime: string | Date;
  endDateTime: string | Date;
}

export async function getRateCardPrices({
  packageIds,
  startDateTime,
  endDateTime,
}: RateCardQueryParams): Promise<Record<string, number>> {
  try {
    if (!startDateTime || !endDateTime) {
      return {};
    }

    const startDate = new Date(startDateTime);
    const endDate = new Date(endDateTime);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      loggerService.warn('Invalid startDateTime or endDateTime provided');
      return {};
    }

    const rateCards: IRateCard[] = await RateCardSchema.find({
      packageId: { $in: packageIds },
      dateTime: {
        $gte: startDate.toISOString(),
        $lte: endDate.toISOString(),
      },
    }).lean();

    loggerService.info(`Fetched ${rateCards.length} rate cards for ${packageIds.length} packages`);

    return rateCards.reduce(
      (acc, rateCard) => {
        const packageId = rateCard.packageId.toString();
        if (!acc[packageId] || acc[packageId] < rateCard.price) {
          acc[packageId] = rateCard.price;
        }
        return acc;
      },
      {} as Record<string, number>,
    );
  } catch (error) {
    loggerService.error(`Failed to fetch rate cards: ${(error as Error).message}`);
    return {};
  }
}
