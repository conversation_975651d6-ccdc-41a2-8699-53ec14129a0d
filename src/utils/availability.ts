import RoomAvailabilityModel from '../models/unit-availability.model';
import { IUnitType } from '../types/models/unit-type.types';
import ReservationItemModel from '../models/reservation-item.model';
import { PaymentStatusEnum, ReservationStatusEnum } from '../types';

export interface AvailabilityCheckParams {
  propertyId: string;
  roomTypes: IUnitType[];
  startDateTime: string;
  endDateTime: string;
}

export interface RoomTypeAvailability {
  unitTypeId: string;
  availableRooms: number;
  totalRooms: number;
}

export async function checkRoomAvailability(params: AvailabilityCheckParams): Promise<RoomTypeAvailability[]> {
  const { propertyId, roomTypes, startDateTime, endDateTime } = params;

  const formattedEndDateTime = new Date(endDateTime).toISOString();
  const formattedStartDateTime = new Date(startDateTime).toISOString();

  const unitTypeIds = roomTypes.map((rt) => rt._id);

  // Get existing reservations
  const reservations = await ReservationItemModel.find({
    propertyId: propertyId,
    unitTypeId: { $in: unitTypeIds },
    startDateTime: { $gte: formattedStartDateTime },
    endDateTime: { $lte: formattedEndDateTime },
    status: ReservationStatusEnum.CONFIRMED,
    paymentStatus: PaymentStatusEnum.PAID,
  }).lean();

  // Get room availabilities
  const roomAvailabilities = await RoomAvailabilityModel.find({
    propertyId: propertyId,
    roomType: { $in: unitTypeIds },
    dateTime: {
      $gte: formattedStartDateTime,
      $lte: formattedEndDateTime,
    },
  }).lean();

  // Generate stay dates
  const stayDates: string[] = [];
  const currentDate = new Date(startDateTime);
  const endDate = new Date(endDateTime);
  currentDate.setUTCHours(0, 0, 0, 0);
  endDate.setUTCHours(0, 0, 0, 0);

  if (currentDate.getTime() === endDate.getTime()) {
    stayDates.push(currentDate.toISOString().split('T')[0]);
  } else {
    while (currentDate < endDate) {
      stayDates.push(currentDate.toISOString().split('T')[0]);
      currentDate.setUTCDate(currentDate.getUTCDate() + 1);
    }
  }

  // Calculate overlapping reservations with buffer time
  const reservationMap = new Map<string, number>();
  reservations.forEach((reservation) => {
    const rtId = reservation.unitTypeId.toString();
    const roomType = roomTypes.find((rt) => rt._id.toString() === rtId);
    const bufferTime = (roomType?.bufferTime || 0) * 60 * 1000;

    const resStart = new Date(reservation.startDateTime);
    const resEnd = new Date(reservation.endDateTime).getTime() + bufferTime;
    const guestStart = new Date(startDateTime);
    const guestEnd = new Date(endDateTime);

    if (resStart < guestEnd && new Date(resEnd) > guestStart) {
      reservationMap.set(rtId, (reservationMap.get(rtId) || 0) + 1);
    }
  });

  // Create availability map
  const availMap = new Map<string, Map<string, number>>();
  roomAvailabilities.forEach((avail) => {
    const rtId = avail.roomType.toString();
    if (!availMap.has(rtId)) {
      availMap.set(rtId, new Map<string, number>());
    }
    const dateMap = availMap.get(rtId);
    if (!dateMap) {
      throw new Error(`Failed to initialize date map for room type ${rtId}`);
    }
    // Use just the date part to match stayDates format
    const dateKey = avail.dateTime.toISOString().split('T')[0];

    // Keep the minimum availability for each date
    const existingAvail = dateMap.get(dateKey);
    const newAvail = existingAvail !== undefined ? Math.min(existingAvail, avail.availability) : avail.availability;

    dateMap.set(dateKey, newAvail);
  });

  // Calculate availability for each room type
  return roomTypes.map((roomType) => {
    const rtId = roomType._id.toString();
    const totalRooms = roomType.totalUnits;
    const reservedCount = reservationMap.get(rtId) || 0;
    let minAvailability = totalRooms;

    for (const date of stayDates) {
      const dateMap = availMap.get(rtId);
      const availForDate = dateMap && dateMap.has(date) ? dateMap.get(date) : totalRooms;

      if (availForDate === undefined) {
        throw new Error(`Availability data missing for room type ${rtId} on date ${date}`);
      }
      minAvailability = Math.min(minAvailability, availForDate);
    }

    const availableRooms = Math.max(0, minAvailability - reservedCount);

    return {
      unitTypeId: rtId,
      availableRooms,
      totalRooms,
    };
  });
}
