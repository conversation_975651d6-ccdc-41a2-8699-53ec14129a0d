import mongoose from 'mongoose';
import { IBase } from './base.types';
import { MonthsEnum } from '../enums';

export interface IPersonalIdProof {
  idType: string;
  idProof: string[];
  idName: string;
}

export interface ISalesTax {
  name: mongoose.Types.ObjectId;
  taxPercentage: number;
}

export interface IBusinessDetails extends IBase {
  propertyId: mongoose.Types.ObjectId;
  registrationNumber: string;
  businessTaxId: string;
  financialYearStart: MonthsEnum;
  financialYearEnd: MonthsEnum;

  registrationDocuments: string[];
  userPersonalId: IPersonalIdProof;
  salesTax: ISalesTax[];
  commission: {
    percentage: number;
    frequency: mongoose.Types.ObjectId;
  };
  serviceCharges: mongoose.Types.ObjectId[];
}
