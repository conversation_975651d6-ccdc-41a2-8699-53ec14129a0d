import mongoose from 'mongoose';
import { IBase } from './base.types';

export interface IPackage extends IBase {
  code: string;
  name: string;
  description: string;
  duration: number;
  propertyId: mongoose.Types.ObjectId;
  unitTypeId: mongoose.Types.ObjectId;
  price: number;
  taxes: mongoose.Types.ObjectId[];
  amenities: mongoose.Types.ObjectId[];

  // Optional
  // Stays
  noOfAdults: number;
  noOfChildren: number;
  extraBed: {
    available: boolean;
    price: number;
  };
  gender: string;
}
