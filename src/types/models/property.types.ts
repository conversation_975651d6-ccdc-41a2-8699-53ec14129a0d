import mongoose, { Types } from 'mongoose';
import { IBase } from './base.types';
import { IAddress } from './common.types';
import { PremisesTypeEnum, PropertyStatus } from '../enums/property.enums';

export interface IProperty extends IBase {
  serviceType: mongoose.Types.ObjectId;
  name: string;
  description: string;
  premisesType: PremisesTypeEnum;
  code: string;
  website: string;
  address: IAddress;
  owner: Types.ObjectId;
  status: PropertyStatus;
  buildYear: Date;
  acceptBookingFrom: Date;
}
