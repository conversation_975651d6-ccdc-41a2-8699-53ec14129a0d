import { IProperty } from '../../types/models/property.types';
import { IReservation, IReservationItem } from '../../types/models/reservation.types';
import { formatCurrency, formatDate, formatTime } from './utils';
import { FRONTEND_DOMAIN_URL } from '../../constants';
import { IPackage, IUnitType } from '../../types';

export const cancelBookingMerchantTemplate = (property: IProperty, reservation: IReservation) => {
  const domainUrl = FRONTEND_DOMAIN_URL;
  const booker = reservation.bookerDetails;

  const reservationItemsHtml = reservation.items
    .map((item: IReservationItem) => {
      const checkIn = new Date(item.startDateTime);
      const checkOut = new Date(item.endDateTime);
      const packageDetails = item.packageId as unknown as IPackage;
      const unitTypeDetails = item.unitTypeId as unknown as IUnitType;
      return `
        <div style="border-top: 1px solid #eee; padding: 10px 0;">
          <p>&bull; <strong>Room/Service:</strong> ${packageDetails?.name || unitTypeDetails?.name || 'N/A'}</p>
          <p>&bull; <strong>Check-in:</strong> ${formatDate(checkIn)} at ${formatTime(checkIn)}</p>
          <p>&bull; <strong>Check-out:</strong> ${formatDate(checkOut)} at ${formatTime(checkOut)}</p>
          <p>&bull; <strong>Guests:</strong> ${item.noOfAdults} Adult(s)${item.noOfChildren > 0 ? `, ${item.noOfChildren} Child(ren)` : ''}</p>
        </div>
      `;
    })
    .join('');

  return `
        <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Booking Cancellation Notice</title>
  <style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f6f7fb; margin: 0; padding: 0; color: #222; }
    .header-bar { background: #fc4f4b; color: #fff; padding: 28px 0 18px 0; text-align: center; }
    .logo { display: inline-block; font-weight: bold; font-size: 28px; color: #fff; letter-spacing: 1px; margin-bottom: 6px; }
    .confirmation { font-size: 22px; font-weight: 700; margin-top: 8px; color: #fff; }
    .main-content { max-width: 600px; margin: 0 auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 32px 32px 0 32px; position: relative; top: -24px; }
    .section-title { font-size: 18px; font-weight: 700; margin: 24px 0 12px 0; color: #222; }
    .booking-id { font-size: 13px; color: #888; margin-bottom: 8px; }
    .property-name { font-size: 16px; font-weight: 600; color: #fc4f4b; margin-bottom: 2px; }
    .details { background: #f6f7fb; border-radius: 8px; padding: 16px; margin: 24px 0; }
    .details p { margin: 5px 0; }
    .details strong { color: #fc4f4b; }
    .button { background: #fc4f4b; text-decoration: none; color: #fff; border: none; border-radius: 6px; padding: 10px 24px; font-size: 15px; font-weight: 700; cursor: pointer; margin-top: 10px; display: inline-block; text-decoration: none; }
    .button:hover { background: #fc4f4b; }
    .footer { background: #f6f7fb; padding: 15px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #eee; }
    .footer a { color: #fc4f4b; text-decoration: none; }
    @media (max-width: 700px) {
      .main-content { padding: 12px !important; }
    }
  </style>
</head>
<body>
  <div class="header-bar">
    <div class="logo">StayTransit</div>
    <div class="confirmation">Booking Cancellation Notice</div>
  </div>
  <div class="main-content">
    <div class="section-title">Cancellation Details <span class="booking-id">(ID: ${reservation.reservationCode})</span></div>
    <div class="property-name">${property.name || 'Hotel Name'}</div>
    <div class="details">
      <p><strong>Primary Booker Information:</strong></p>
      <p>&bull; Name: ${booker.firstName || 'N/A'} ${booker.lastName || ''}</p>
      <p>&bull; Email: ${booker.email || 'N/A'}</p>
      <p>&bull; Phone: ${booker.phone ? `${booker.phone.countryCode} ${booker.phone.phoneNumber}` : 'N/A'}</p>
    </div>
    <div class="details">
      <p><strong>Cancelled Booking Items:</strong></p>
      ${reservationItemsHtml}
    </div>
    <p>We regret to inform you that a booking has been cancelled by the guest on StayTransit. Please update your availability in the StayTransit Partner Panel.</p>
    <a href="${domainUrl}" class="button">Access Partner Panel</a>
    <p>For any questions, our support team is here to assist you.<br>Best regards,<br>StayTransit Team</p>
    <div class="footer">
      © 2025 StayTransit. All rights reserved. <a href="https://staytransit.com/support">Contact Support</a>
    </div>
  </div>
</body>
</html>
    `;
};

export const cancelBookingGuestTemplate = (property: IProperty, reservation: IReservation) => {
  const totalCouponDiscount = reservation.items.reduce((acc, item) => acc + (item.couponDiscount || 0), 0);

  const reservationItemsHtml = reservation.items
    .map((item: IReservationItem) => {
      const checkIn = new Date(item.startDateTime);
      const checkOut = new Date(item.endDateTime);
      const packageDetails = item.packageId as unknown as IPackage;
      const unitTypeDetails = item.unitTypeId as unknown as IUnitType;
      return `
        <div style="border-bottom: 1px solid #eee; padding-bottom: 15px; margin-bottom: 15px;">
            <div style="font-size: 16px; font-weight: 600; color: #333;">${packageDetails?.name || unitTypeDetails?.name || 'Room'}</div>
            <ul style="list-style: none; padding-left: 0; font-size: 14px; color: #555;">
                <li>Guests: ${item.noOfAdults || 1} Adult${item.noOfChildren > 0 ? `, ${item.noOfChildren} Children` : ''}</li>
                <li>Check-in: ${formatDate(checkIn)} at ${formatTime(checkIn)}</li>
                <li>Check-out: ${formatDate(checkOut)} at ${formatTime(checkOut)}</li>
            </ul>
        </div>
      `;
    })
    .join('');

  return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reservation Cancellation - StayTransit</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f6f7fb; margin: 0; padding: 0; color: #222; }
                .header-bar { background: #fc4f4b; color: #fff; padding: 28px 0 18px 0; text-align: center; }
                .logo { display: inline-block; font-weight: bold; font-size: 28px; color: #fff; letter-spacing: 1px; margin-bottom: 6px; }
                .confirmation { font-size: 22px; font-weight: 700; margin-top: 8px; color: #fff; }
                .greeting { font-size: 15px; color: #fff; margin-top: 4px; }
                .main-content { max-width: 600px; margin: 0 auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 32px 32px 0 32px; position: relative; top: -24px; }
                .section-title { font-size: 18px; font-weight: 700; margin: 24px 0 12px 0; color: #222; }
                .booking-id { font-size: 13px; color: #888; margin-bottom: 8px; }
                .property-name { font-size: 16px; font-weight: 600; color: #fc4f4b; margin-bottom: 2px; }
                .payment-table { width: 100%; border-collapse: collapse; margin: 18px 0; }
                .payment-table td { padding: 10px 0; font-size: 15px; border-bottom: 1px solid #f0f0f0; }
                .payment-table .label { color: #555; }
                .payment-table .total { font-weight: bold; color: #222; font-size: 17px; border-top: 2px solid #fc4f4b; }
                .footer { background: #f6f7fb; padding: 15px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #eee; }
                .footer a { color: #fc4f4b; text-decoration: none; }
                @media (max-width: 700px) {
                    .main-content { padding: 12px !important; }
                }
            </style>
        </head>
        <body>
            <div class="header-bar">
                <div class="logo">StayTransit</div>
                <div class="confirmation">Your reservation has been cancelled.</div>
                <div class="greeting">Hello ${reservation.bookerDetails.firstName}, We regret to inform you about the cancellation.</div>
            </div>
            <div class="main-content">
                <div class="section-title">Cancelled Booking Details <span class="booking-id">(ID: ${reservation.reservationCode})</span></div>
                <div class="property-name">${property.name}</div>
                ${reservationItemsHtml}
                <div class="section-title">Payment Summary</div>
                <table class="payment-table">
                    <tr><td class="label">Subtotal</td><td style="text-align:right;">${formatCurrency(reservation.subTotal)}</td></tr>
                    <tr><td class="label">Coupon Discount</td><td style="text-align:right;">-${formatCurrency(totalCouponDiscount)}</td></tr>
                    <tr><td class="label">Taxes & Charges</td><td style="text-align:right;">${formatCurrency(reservation.totalTax)}</td></tr>
                    ${reservation.refundAmount && reservation.refundAmount > 0 ? `<tr><td class="label">Refund Amount</td><td style="text-align:right;">${formatCurrency(reservation.refundAmount)}</td></tr>` : ''}
                    <tr class="total-row"><td class="label total">Total Paid</td><td class="total" style="text-align:right;">${formatCurrency(reservation.grandTotal)}</td></tr>
                </table>
                <p>Your booking has been cancelled. We're listing the details for your cancellation below. It's sad we won't be seeing you. We hope to see you in the future.</p>
                <div class="footer">
                    © 2025 StayTransit. All rights reserved. <a href="https://staytransit.com/support">Contact Support</a> | <a href="https://staytransit.com/privacy">Privacy Policy</a>
                </div>
            </div>
        </body>
        </html>
    `;
};
