import { z } from 'zod';

// Extra bed schema
const extraBedSchema = z.object({
  available: z.boolean(),
  price: z.number().min(0, 'Extra bed price must be non-negative'),
});

// Common package fields
const commonPackageFields = {
  unitTypeId: z.string().min(1, 'Unit type ID is required'),
  name: z.string().min(1, 'Name is required'),
  description: z.string().default('').optional(),
  duration: z.number().min(1, 'Duration must be at least 1'),
  price: z.number().min(0, 'Price must be non-negative'),
  taxes: z.array(z.string()),
  amenities: z.array(z.string()),
};

// Common create package schema
export const commonCreatePackageSchema = z.object(commonPackageFields);

// Stays package schema (hotel)
export const createStaysPackageSchema = commonCreatePackageSchema.extend({
  noOfAdults: z.number().default(1),
  noOfChildren: z.number().default(0),
  extraBed: extraBedSchema,
});

// Capsules package schema
export const createCapsulesPackageSchema = commonCreatePackageSchema.extend({
  gender: z.string().min(1, 'Gender is required'),
});

// Spa package schema
export const createSpaPackageSchema = commonCreatePackageSchema.extend({
  gender: z.string().min(1, 'Gender is required'),
});

// Common update package fields
const commonUpdatePackageFields = {
  unitTypeId: z.string().optional(),
  name: z.string().optional(),
  description: z.string().default('').optional(),
  duration: z.number().min(1, 'Duration must be at least 1').optional(),
  price: z.number().min(0, 'Price must be non-negative').optional(),
  taxes: z.array(z.string()).optional(),
  amenities: z.array(z.string()).optional(),
};

// Common update package schema
export const commonUpdatePackageSchema = z.object(commonUpdatePackageFields);

// Update stays package schema
export const updateStaysPackageSchema = commonUpdatePackageSchema.extend({
  noOfAdults: z.number().optional(),
  noOfChildren: z.number().optional(),
  extraBed: extraBedSchema.optional(),
});

// Update capsules package schema
export const updateCapsulesPackageSchema = commonUpdatePackageSchema.extend({
  gender: z.string().optional(),
});

// Update spa package schema
export const updateSpaPackageSchema = commonUpdatePackageSchema.extend({
  gender: z.string().optional(),
});

export const getPackageCreateSchema = (serviceTypeName: string) => {
  switch (serviceTypeName.toLowerCase()) {
    case 'hotel':
      return createStaysPackageSchema;
    case 'capsule':
      return createCapsulesPackageSchema;
    case 'spa':
      return createSpaPackageSchema;
    default:
      return commonCreatePackageSchema;
  }
};

export const getPackageUpdateSchema = (serviceTypeName: string) => {
  switch (serviceTypeName.toLowerCase()) {
    case 'hotel':
      return updateStaysPackageSchema;
    case 'capsule':
      return updateCapsulesPackageSchema;
    case 'spa':
      return updateSpaPackageSchema;
    default:
      return commonUpdatePackageSchema;
  }
};

export type CommonPackageInput = z.infer<typeof commonCreatePackageSchema>;
export type StaysPackageInput = z.infer<typeof createStaysPackageSchema>;
export type CapsulesPackageInput = z.infer<typeof createCapsulesPackageSchema>;
export type SpaPackageInput = z.infer<typeof createSpaPackageSchema>;
export type CommonPackageUpdateInput = z.infer<typeof commonUpdatePackageSchema>;
export type StaysPackageUpdateInput = z.infer<typeof updateStaysPackageSchema>;
export type CapsulesPackageUpdateInput = z.infer<typeof updateCapsulesPackageSchema>;
export type SpaPackageUpdateInput = z.infer<typeof updateSpaPackageSchema>;
