import { z } from 'zod';

// Phone schema
const phoneSchema = z.object({
  countryCode: z.string().min(1, 'Country code is required'),
  phoneNumber: z.string().min(1, 'Phone number is required'),
});

// Guest details schema
const guestDetailsSchema = z
  .object({
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    gender: z.enum(['male', 'female', 'other']).optional(),
    email: z.string().email('Invalid email format').optional(),
    phone: phoneSchema.optional(),
  })
  .passthrough(); // Allow unknown properties

// Get OTP validation schema
export const getOtpValidationSchema = z.object({
  reservationCode: z.string().min(1, 'Reservation code is required'),
});

// Verify OTP validation schema
export const verifyOtpValidationSchema = z.object({
  reservationCode: z.string().min(1, 'Reservation code is required'),
  otp: z.string().min(1, 'OTP is required'),
});

// Reservation update schema
export const updateValidationSchema = z.object({
  guestDetails: z.array(guestDetailsSchema).optional(),
});

export type GetOtpInput = z.infer<typeof getOtpValidationSchema>;
export type VerifyOtpInput = z.infer<typeof verifyOtpValidationSchema>;
export type ReservationUpdateInput = z.infer<typeof updateValidationSchema>;
