import { z } from 'zod';

// Policy option schema
const policyOptionSchema = z.object({
  value: z.string().trim().min(1, 'Option value is required'),
  description: z.string().trim().optional().or(z.literal('')),
});

// Validation rules schema
const validationRulesSchema = z
  .object({
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional().or(z.literal('')),
    required: z.boolean().optional(),
  })
  .optional();

// Conditional logic schema
const conditionalLogicSchema = z
  .object({
    field: z.string().min(1, 'Field is required'),
    value: z.union([z.string(), z.number(), z.boolean()]),
    operator: z.enum(['equals', 'not_equals', 'contains', 'greater_than', 'less_than']),
  })
  .optional();

// Policy validation schema
export const policyValidationSchema = z.object({
  name: z.string().trim().min(1, 'Name is required').max(100, 'Name must be at most 100 characters'),
  description: z.string().trim().max(500, 'Description must be at most 500 characters').optional().or(z.literal('')),
  category: z.string().optional().or(z.literal('')),
  serviceType: z.string().min(1, 'Service type is required'),
  order: z.number().default(0).optional(),
  input_type: z.enum(['text', 'radio', 'checkbox', 'select', 'custom_rule']),
  options: z.array(policyOptionSchema).optional(),
  is_required: z.boolean(),
  validation_rules: validationRulesSchema,
  conditional_logic: conditionalLogicSchema,
  config: z.object({}).optional(),
  propertyId: z.string().optional(),
});

export type PolicyInput = z.infer<typeof policyValidationSchema>;
