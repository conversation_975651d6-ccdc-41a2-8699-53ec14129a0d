import { z } from 'zod';

// Common unit type fields
const commonCreateFields = {
  code: z.string().min(1, 'Code is required'),
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  totalUnits: z.number().default(1).optional(),
  attachments: z.array(z.string()),
  bufferTime: z.number().default(0),
};

// Common create schema
export const commonCreateFieldsSchema = z.object(commonCreateFields);

// Room type create schema (hotel)
export const roomTypeCreateSchema = commonCreateFieldsSchema.extend({
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  bathroomType: z.string().optional(),
  isSmokingAllowed: z.boolean().optional(),
  bedType: z.string().min(1, 'Bed type is required'),
  area: z.number().min(1, 'Area must be at least 1'),
});

// Capsule type create schema
export const capsuleTypeCreateSchema = commonCreateFieldsSchema.extend({
  bedType: z.string().min(1, 'Bed type is required'),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
});

// Workspace type create schema
export const workspaceTypeCreateSchema = commonCreateFieldsSchema.extend({
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  area: z.number().min(1, 'Area must be at least 1'),
});

// Shower type create schema
export const showerTypeCreateSchema = commonCreateFieldsSchema.extend({
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  area: z.number().min(1, 'Area must be at least 1'),
});

// Common update fields
const commonUpdateFields = {
  code: z.string().optional(),
  name: z.string().optional(),
  description: z.string().optional(),
  totalUnits: z.number().optional(),
  attachments: z.array(z.string()).optional(),
  bufferTime: z.number().optional(),
};

// Common update schema
export const commonUpdateFieldsSchema = z.object(commonUpdateFields);

// Room type update schema
export const roomTypeUpdateSchema = commonUpdateFieldsSchema.extend({
  capacity: z.number().optional(),
  bathroomType: z.string().optional(),
  isSmokingAllowed: z.boolean().optional(),
  bedType: z.string().optional(),
  area: z.number().optional(),
});

// Capsule type update schema
export const capsuleTypeUpdateSchema = commonUpdateFieldsSchema.extend({
  bedType: z.string().optional(),
  capacity: z.number().optional(),
});

// Workspace type update schema
export const workspaceTypeUpdateSchema = commonUpdateFieldsSchema.extend({
  capacity: z.number().optional(),
  area: z.number().optional(),
});

// Shower type update schema
export const showerTypeUpdateSchema = commonUpdateFieldsSchema.extend({
  capacity: z.number().optional(),
  area: z.number().optional(),
});

export const getUnitTypeCreateSchema = (serviceTypeName: string) => {
  switch (serviceTypeName) {
    case 'Hotel':
      return roomTypeCreateSchema;
    case 'Capsule':
      return capsuleTypeCreateSchema;
    case 'Workspace':
      return workspaceTypeCreateSchema;
    case 'Shower':
      return showerTypeCreateSchema;
    default:
      return commonCreateFieldsSchema;
  }
};

export const getUnitTypeUpdateSchema = (serviceTypeName: string) => {
  switch (serviceTypeName) {
    case 'Hotel':
      return roomTypeUpdateSchema;
    case 'Capsule':
      return capsuleTypeUpdateSchema;
    case 'Workspace':
      return workspaceTypeUpdateSchema;
    case 'Shower':
      return showerTypeUpdateSchema;
    default:
      return commonUpdateFieldsSchema;
  }
};

export type CommonUnitTypeInput = z.infer<typeof commonCreateFieldsSchema>;
export type RoomTypeInput = z.infer<typeof roomTypeCreateSchema>;
export type CapsuleTypeInput = z.infer<typeof capsuleTypeCreateSchema>;
export type WorkspaceTypeInput = z.infer<typeof workspaceTypeCreateSchema>;
export type ShowerTypeInput = z.infer<typeof showerTypeCreateSchema>;
export type CommonUnitTypeUpdateInput = z.infer<typeof commonUpdateFieldsSchema>;
export type RoomTypeUpdateInput = z.infer<typeof roomTypeUpdateSchema>;
export type CapsuleTypeUpdateInput = z.infer<typeof capsuleTypeUpdateSchema>;
export type WorkspaceTypeUpdateInput = z.infer<typeof workspaceTypeUpdateSchema>;
export type ShowerTypeUpdateInput = z.infer<typeof showerTypeUpdateSchema>;
