import { z } from 'zod';
import { PremisesTypeEnum } from '../types/enums/property.enums';

// Address schema
const addressSchema = z.object({
  address1: z.string().min(1, 'Address line 1 is required'),
  address2: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  country: z.string().min(1, 'Country is required'),
  zipcode: z.string().optional().or(z.literal('')),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  placeId: z.string().optional().or(z.literal('')),
  locationId: z.string().optional(),
});

// Main property schema
export const propertySchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(50, 'Name must be at most 50 characters'),
  description: z.string().optional(),
  website: z.string().optional(),
  premisesType: z.enum(PremisesTypeEnum),
  address: addressSchema,
  buildYear: z.coerce.date(),
  acceptBookingFrom: z.coerce.date(),
  serviceType: z.string().min(1, 'Service type is required'),
});

export type PropertyInput = z.infer<typeof propertySchema>;
