import { z } from 'zod';

export const domainValueSchema = z.object({
  propertyId: z.string().optional(),
  category: z.string().optional().or(z.literal('')),
  level: z.string().min(1, 'Level is required'),
  code: z.string().min(1, 'Code is required'),
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional().or(z.literal('')),
  displayOrder: z.number().optional(),
  categoryId: z.string().optional().or(z.literal('')),
  parentId: z.string().optional().or(z.literal('')),
  value: z.string().optional().or(z.literal('')),
  icon: z.string().optional().or(z.literal('')),
});

export type DomainValueInput = z.infer<typeof domainValueSchema>;
