import { randomBytes } from 'node:crypto';
import jwt from 'jsonwebtoken';
import {
  JWT_ACCESS_TOKEN_EXPIRATION_TIME,
  JWT_ACCESS_TOKEN_SECRET,
  JWT_ALGORITHM,
  JWT_AUDIENCE,
  JWT_ISSUER,
  JWT_REFRESH_TOKEN_EXPIRATION_TIME,
  JWT_REFRESH_TOKEN_SECRET,
} from '../constants';

interface JwtPayloadWithUserId extends jwt.JwtPayload {
  userId: string;
}

export interface JwtPayloadRequest extends jwt.JwtPayload {
  userId: string;
  role: string;
  properties?: string[];
}

export class JwtService {
  private static instance: JwtService;

  public static getInstance(): JwtService {
    if (!JwtService.instance) {
      JwtService.instance = new JwtService();
    }
    return JwtService.instance;
  }

  private constructor() {}

  generateAccessToken({ userId, role, properties }: JwtPayloadRequest): string {
    try {
      const payload = {
        userId: userId.trim(),
        role: role,
        properties: properties,
        iat: Math.floor(Date.now() / 1000),
        jti: randomBytes(16).toString('hex'),
      };

      const tokenOptions: jwt.SignOptions = {
        expiresIn: JWT_ACCESS_TOKEN_EXPIRATION_TIME,
        algorithm: JWT_ALGORITHM,
        issuer: JWT_ISSUER,
        audience: JWT_AUDIENCE,
        subject: userId.toString(),
      };

      return jwt.sign(payload, JWT_ACCESS_TOKEN_SECRET, tokenOptions);
    } catch (error) {
      throw new Error(`Failed to generate JWT: ${(error as Error).message}`);
    }
  }

  verifyAccessToken(token: string): JwtPayloadWithUserId {
    try {
      const verifyOptions: jwt.VerifyOptions = {
        algorithms: [JWT_ALGORITHM],
        issuer: JWT_ISSUER,
        audience: JWT_AUDIENCE,
      };

      return jwt.verify(token.trim(), JWT_ACCESS_TOKEN_SECRET, verifyOptions) as JwtPayloadWithUserId;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('JWT token has expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error(`Invalid JWT token: ${error.message}`);
      } else if (error instanceof jwt.NotBeforeError) {
        throw new Error('JWT token not yet valid');
      } else {
        throw new Error(`Failed to verify JWT: ${(error as Error).message}`);
      }
    }
  }

  generateRefreshToken({ userId, role, properties }: JwtPayloadRequest): string {
    try {
      const jti = randomBytes(16).toString('hex');
      const payload = {
        userId: userId.trim(),
        role: role,
        properties: properties,
        iat: Math.floor(Date.now() / 1000),
        jti,
      };

      const tokenOptions: jwt.SignOptions = {
        expiresIn: JWT_REFRESH_TOKEN_EXPIRATION_TIME,
        algorithm: JWT_ALGORITHM,
        issuer: JWT_ISSUER,
        audience: JWT_AUDIENCE,
        subject: userId,
      };

      return jwt.sign(payload, JWT_REFRESH_TOKEN_SECRET, tokenOptions);
    } catch (error) {
      throw new Error(`Failed to generate refresh token: ${(error as Error).message}`);
    }
  }

  verifyRefreshToken(token: string): JwtPayloadWithUserId {
    try {
      const verifyOptions: jwt.VerifyOptions = {
        algorithms: [JWT_ALGORITHM],
        issuer: JWT_ISSUER,
        audience: JWT_AUDIENCE,
      };

      const decoded = jwt.verify(token.trim(), JWT_REFRESH_TOKEN_SECRET, verifyOptions) as JwtPayloadWithUserId;

      if (!decoded.userId || !decoded.jti) {
        throw new Error('Invalid refresh token payload: missing required fields');
      }

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Refresh token has expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error(`Invalid refresh token: ${error.message}`);
      } else if (error instanceof jwt.NotBeforeError) {
        throw new Error('Refresh token not yet valid');
      } else {
        throw new Error(`Failed to verify refresh token: ${(error as Error).message}`);
      }
    }
  }
}
