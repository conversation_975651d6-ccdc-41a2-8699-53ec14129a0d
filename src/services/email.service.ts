import nodemailer from 'nodemailer';
import loggerService from '../utils/logger/logger.service';
import { IProperty, IPropertyCancellationPolicy, IReservation, IUser } from '../types';
import {
  cancelBookingGuestTemplate,
  cancelBookingMerchantTemplate,
  reservationTemplate,
  updateBookingHostTemplate,
  updateReservationGuestTemplate,
} from '../templates/reservation';
import { approvalTemplate } from '../templates/approval.template';
import { activeStatusTemplate } from '../templates/active-status.template';
import { forgotPasswordTemplate } from '../templates/forgot-password.template';
import { otpVerificationTemplate } from '../templates/otp-verification.template';
import { MAIL_IS_SECURE, MAIL_PASSWORD, MAIL_SMTP_HOST, MAIL_SMTP_PORT, MAIL_USERNAME } from '../constants';

export class EmailService {
  constructor() {}

  async sendConfirmationEmail(to: string, firstName: string): Promise<void> {
    try {
      const html = `
          <h1>Welcome to StayTransit, ${firstName}!</h1>
          <p>Your account has been successfully created.</p>
          <p>Thank you for joining us!</p>
        `;
      await this.sendMail(to, 'Welcome to StayTransit - Account Confirmation', html);

      loggerService.info(`Confirmation email sent to ${to}`);
    } catch (error) {
      loggerService.error(`Failed to send confirmation email to ${to}: ${(error as Error).message}`);
      throw error;
    }
  }

  async sendReservationConfirmationEmail(
    to: string,
    firstName: string,
    reservation: IReservation,
    property: IProperty,
    cancellationPolicies: IPropertyCancellationPolicy[],
  ): Promise<void> {
    try {
      const html = reservationTemplate(firstName, reservation, property, cancellationPolicies);

      await this.sendMail(to, 'StayTransit - Reservation Confirmation', html);
      loggerService.info(`Reservation confirmation email sent to ${to} for reservation ${reservation.reservationCode}`);
    } catch (error) {
      loggerService.error(`Failed to send reservation confirmation email to ${to}: ${(error as Error).message}`);
      throw error;
    }
  }

  async sendApprovalStatusEmail(
    to: string,
    firstName: string,
    property: IProperty,
    status: 'Approved' | 'Rejected' | 'Pending',
  ): Promise<void> {
    try {
      const html = approvalTemplate(firstName, status);
      const subject =
        status === 'Approved' ? 'StayTransit - Property Approved!' : 'StayTransit - Property Application Update';

      await this.sendMail(to, subject, html);
      loggerService.info(`Approval status email sent to ${to} for property ${property.name} - Status: ${status}`);
    } catch (error) {
      loggerService.error(`Failed to send approval status email to ${to}: ${(error as Error).message}`);
      throw error;
    }
  }

  async sendActiveStatusEmail(to: string, firstName: string, property: IProperty, isActive: boolean): Promise<void> {
    try {
      const html = activeStatusTemplate(firstName, isActive);
      const subject = isActive ? 'StayTransit - Property Activated!' : 'StayTransit - Property Deactivated';

      await this.sendMail(to, subject, html);
      loggerService.info(`Active status email sent to ${to} for property ${property.name} - Active: ${isActive}`);
    } catch (error) {
      loggerService.error(`Failed to send active status email to ${to}: ${(error as Error).message}`);
      throw error;
    }
  }

  async sendForgotPasswordEmail(to: string, firstName: string, token: string): Promise<void> {
    try {
      const html = forgotPasswordTemplate(firstName, token);

      await this.sendMail(to, 'StayTransit - Password Reset Request', html);

      loggerService.info(`Password reset email sent to ${to}`);
    } catch (error) {
      loggerService.error(`Failed to send password reset email to ${to}: ${(error as Error).message}`);
      throw error;
    }
  }

  async sendCancellationEmail(
    to: string,
    reservation: IReservation,
    property: IProperty,
    sendToMerchant: boolean = true,
  ): Promise<void> {
    const poc = property.owner as unknown as IUser;

    try {
      const guestHtml = cancelBookingGuestTemplate(property, reservation);
      await this.sendMail(to, `Reservation Cancellation - ${reservation.reservationCode}`, guestHtml);
      if (sendToMerchant) {
        const merchantHtml = cancelBookingMerchantTemplate(property, reservation);
        await this.sendMail(poc.email, `Reservation Cancellation - ${reservation.reservationCode}`, merchantHtml);
      }
      loggerService.info(`Cancellation email sent to ${to} for reservation ${reservation.reservationCode}`);
    } catch (error) {
      loggerService.error(`Failed to send cancellation email to ${to}: ${(error as Error).message}`);
      throw error;
    }
  }

  async sendOtpVerificationEmail(to: string, firstName: string, otp: string): Promise<void> {
    try {
      const html = otpVerificationTemplate(firstName, otp);
      await this.sendMail(to, 'StayTransit - OTP Verification', html);

      loggerService.info(`OTP verification email sent to ${to}`);
    } catch (error) {
      loggerService.error(`Failed to send OTP verification email to ${to}: ${(error as Error).message}`);
    }
  }

  async sendUpdateReservationEmail(
    to: string,
    reservation: IReservation,
    property: IProperty,
    sendToMerchant: boolean = true,
    cancellationPolicies: IPropertyCancellationPolicy[],
  ): Promise<void> {
    try {
      const poc = property.owner as unknown as IUser;
      const html = updateReservationGuestTemplate(reservation, property, cancellationPolicies);
      await this.sendMail(to, `Reservation Updated - ${reservation.reservationCode}`, html);
      loggerService.info(`Reservation update email sent to ${to} for reservation ${reservation.reservationCode}`);
      if (sendToMerchant) {
        const merchantHtml = updateBookingHostTemplate(property, reservation);
        await this.sendMail(poc.email, `Reservation Updated -  ${reservation.reservationCode}`, merchantHtml);
      }
      loggerService.info(
        `Reservation update email sent to ${poc.email} for reservation ${reservation.reservationCode}`,
      );
    } catch (error) {
      loggerService.error(`Failed to send reservation update email: ${(error as Error).message}`);
      throw error;
    }
  }

  async sendMail(to: string, subject: string, html: string): Promise<void> {
    try {
      const transporter = nodemailer.createTransport({
        host: MAIL_SMTP_HOST,
        port: MAIL_SMTP_PORT,
        secure: MAIL_IS_SECURE,
        requireTLS: MAIL_SMTP_PORT === 587,
        auth: {
          user: MAIL_USERNAME,
          pass: MAIL_PASSWORD,
        },
      } as nodemailer.TransportOptions);

      await transporter.sendMail({
        from: `"StayTransit" <${MAIL_USERNAME}>`,
        to: to,
        subject: subject,
        html: html,
      });
      loggerService.info(`Email sent to ${to}`);
    } catch (error) {
      loggerService.error(`Failed to send email to ${to}: ${(error as Error).message}`);
      throw error;
    }
  }
}
