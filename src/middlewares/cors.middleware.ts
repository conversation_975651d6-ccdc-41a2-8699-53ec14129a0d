import cors from 'cors';
import { IRequest } from '../types';
import { ALLOWED_ORIGINS, NODE_ENV } from '../constants';
import loggerService from '../utils/logger/logger.service';

export const corsMiddleware = cors((req: IRequest, callback) => {
  const origin = req.headers.origin;
  const requestUrl = req.originalUrl;

  const corsOptions = {
    origin: false,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'skip_zrok_interstitial'],
    credentials: true,
  };

  // Allow requests from explicitly allowed origins
  if (typeof origin === 'string' && ALLOWED_ORIGINS.includes(origin)) {
    corsOptions.origin = true;
    return callback(null, corsOptions);
  }

  // Allow requests with no Origin header in development/QA
  else if (origin === undefined && NODE_ENV === 'QA') {
    corsOptions.origin = true;
    return callback(null, corsOptions);
  }

  loggerService.warn(`CORS denied for origin: ${origin}, URL: ${requestUrl}`);

  return callback(new Error('CORS Error'));
});
