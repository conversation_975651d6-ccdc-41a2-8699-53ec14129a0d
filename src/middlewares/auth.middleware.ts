import loggerService from '../utils/logger/logger.service';
import { JwtService } from '../services/jwt.service';
import { IS_COOKIE_SECURE } from '../constants';
import { IRequest, IResponse, INextFunction } from '../types';
import { ResponseUtil } from '../utils/response';

const jwtService = JwtService.getInstance();

export const authMiddleware = (req: IRequest, res: IResponse, next: INextFunction): void => {
  const accessToken = req.cookies?.accessToken;

  if (!accessToken) {
    loggerService.warn('No token provided');
    return ResponseUtil.unauthorized(res, 'No token provided');
  }

  try {
    const decoded = jwtService.verifyAccessToken(accessToken);
    req.user = { userId: decoded.userId, role: decoded.role };
    next();
  } catch (error) {
    const errorMessage = (error as Error).message;
    loggerService.error(`Failed to verify token: ${errorMessage}`);

    if (errorMessage.includes('expired')) {
      const refreshToken = req.cookies?.refreshToken;
      if (!refreshToken) {
        return ResponseUtil.unauthorized(res, 'No refresh token provided');
      }

      try {
        const decodedRefresh = jwtService.verifyRefreshToken(refreshToken);

        const newAccessToken = jwtService.generateAccessToken({
          userId: decodedRefresh.userId,
          role: decodedRefresh.role,
        });

        res.cookie('accessToken', newAccessToken, {
          httpOnly: true,
          secure: IS_COOKIE_SECURE,
          sameSite: 'strict',
          maxAge: 15 * 60 * 1000,
          path: '/',
        });

        loggerService.info(`Access token refreshed for user: ${decodedRefresh.userId}`);
        req.user = { userId: decodedRefresh.userId, role: decodedRefresh.role };
        next();
      } catch (refreshError) {
        loggerService.error(`Failed to verify refresh token: ${(refreshError as Error).message}`);
        ResponseUtil.unauthorized(res, 'Invalid refresh token');
      }
    } else {
      ResponseUtil.unauthorized(res, 'Invalid access token');
    }
  }
};
