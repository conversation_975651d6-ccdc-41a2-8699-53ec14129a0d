import { z } from 'zod';
import { IDomainValue, INextFunction, IRequest, IResponse } from '../../types';
import { ResponseUtil } from '../../utils/response';
import PropertyModel from '../../models/property.model';

export async function validateBodyByServiceType(
  req: IRequest,
  res: IResponse,
  next: INextFunction,
  getSchema: (serviceTypeName: string) => z.ZodObject<z.ZodRawShape>,
): Promise<void> {
  try {
    const propertyId = req.params.propertyId;

    if (!propertyId) {
      return ResponseUtil.badRequest(res, 'Property ID is required');
    }

    const property = await PropertyModel.findById(propertyId).populate<{
      serviceType: IDomainValue;
    }>('serviceType');

    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    if (!property.serviceType || !property.serviceType.name) {
      return ResponseUtil.badRequest(res, 'Service type or name not found for the property');
    }

    const schema = getSchema(property.serviceType.name);
    await schema.parseAsync(req.body);
    next();
  } catch (error) {
    if (error instanceof z.ZodError) {
      return ResponseUtil.badRequest(res, 'Validation failed', error);
    }
    next(error);
  }
}
