import { Router } from 'express';
import { PaymentController } from '../controllers/payment.controller';
import { validateBody } from '../middlewares/validators/validation.middleware';
import { paymentSchema } from '../validators/payment.validator';

const paymentController = PaymentController.getInstance();

const router: Router = Router();
router.route('/').post(validateBody(paymentSchema), paymentController.create);

router
  .route('/:id')
  .get(paymentController.get)
  .put(validateBody(paymentSchema), paymentController.update)
  // .patch(paymentController.updateStatus)
  .delete(paymentController.delete);

router.route('/:paymentProvider/checkout').post(paymentController.checkout);

router.post('/:reservationId/:paymentProvider/refund', paymentController.refund);

export default router;
