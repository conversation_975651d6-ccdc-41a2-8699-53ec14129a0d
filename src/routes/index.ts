import { Router } from 'express';

// Routes
import authenticationRoutes from './authentication.routes';
import imageRoutes from './image.routes';
import propertyRoutes from './property.routes';
import reservationRoutes from './reservation.routes';
import unitTypeRoutes from './unit-type.routes';
import packageRoutes from './package.routes';
import userRoutes from './user.routes';
import domainValueRoutes from './domain-value.routes';
import policyRoutes from './policy.routes';
import locationRoutes from './location.routes';
import paymentRoutes from './payment.routes';
import rateCardRoutes from './rate-card.routes';
import financeRoutes from './finance.route';
import amenityRoutes from './amenity.routes';
import { authMiddleware } from '../middlewares/auth.middleware';
import { financeAdminMiddleware } from '../middlewares/rbac/finance-admin.middleware';

const router: Router = Router();
router.use('/v1/images', imageRoutes);
router.use('/v1/properties', propertyRoutes);
router.use('/v1/properties/:propertyId/unit-types', unitTypeRoutes);
router.use('/v1/properties/:propertyId/packages', packageRoutes);
router.use('/v1/properties/:propertyId/rate-cards', rateCardRoutes);
router.use('/v1/reservations', reservationRoutes);
router.use('/v1/payments', paymentRoutes);

router.use('/v1/users', userRoutes);
router.use('/v1/domain-values', domainValueRoutes);
router.use('/v1/amenities', amenityRoutes);
router.use('/v1/authentication', authenticationRoutes);
router.use('/v1/policy', policyRoutes);

//
router.use('/v1/locations', locationRoutes);
router.use('/v1/finance', authMiddleware, financeAdminMiddleware, financeRoutes);

export default router;
