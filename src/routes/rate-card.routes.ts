import { Router, Request, Response, NextFunction } from 'express';
import { RateCardController } from '../controllers/rate-card.controller';
import { rateCardSchema } from '../validators/rate-card.validator';
import loggerService from '../utils/logger/logger.service';
import { ResponseUtil } from '../utils/response';

// Custom validation middleware for rate cards
const validateRateCards = (req: Request, res: Response, next: NextFunction): void => {
  try {
    const rateCards = req.body.rateCards;
    const result = rateCardSchema.safeParse(rateCards);

    if (!result.success) {
      const errorMessage = result.error.issues[0]?.message || 'Invalid rate cards data';
      loggerService.warn(`Rate cards validation failed: ${errorMessage}`, 'rate-card.routes.ts', {
        path: req.path,
        method: req.method,
        errors: result.error.issues,
      });
      ResponseUtil.badRequest(res, 'Invalid rate cards data', errorMessage);
      return;
    }

    req.body.rateCards = result.data;
    next();
  } catch (error) {
    const errorMessage = (error as Error).message;
    loggerService.error('Rate cards validation middleware error:', errorMessage);
    ResponseUtil.internalServerError(res, 'Internal Server Error', errorMessage);
  }
};

const router: Router = Router({ mergeParams: true });
const rateCardController = RateCardController.getInstance();

router.route('/').get(rateCardController.getByDate).post(validateRateCards, rateCardController.create);

export default router;
