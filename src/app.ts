import express, { Application } from 'express';
import helmet from 'helmet';
import routes from './routes';
import cookieParser from 'cookie-parser';
import { corsMiddleware } from './middlewares/cors.middleware';
import { IRequest, IResponse } from './types';
import { errorHandler } from './middlewares/error.middleware';
import { requestLogger } from './middlewares/requests-logger.middleware';

const app: Application = express();

app.use(express.json());
app.use(corsMiddleware);
app.use(helmet());
app.use(cookieParser());

app.use(requestLogger);

app.get('/', (_req: IRequest, res: IResponse) => {
  res.json({
    message: 'Welcome to Stay Transit Backend',
    version: '1.0.0',
  });
});
app.use('/api', routes);

app.use(errorHandler);

export default app;
