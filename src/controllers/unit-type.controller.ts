import { IDomainValue, IRequest, IResponse, IUnitType } from '../types';
import UnitType from '../models/unit-type.model';
import mongoose from 'mongoose';
import PropertyModel from '../models/property.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class UnitTypeController {
  private static Instance: UnitTypeController;

  public static getInstance(): UnitTypeController {
    if (!UnitTypeController.Instance) {
      UnitTypeController.Instance = new UnitTypeController();
    }
    return UnitTypeController.Instance;
  }

  private constructor() {}

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;

    // Validate if propertyId is a valid MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(propertyId)) {
      return ResponseUtil.badRequest(res, 'Invalid property ID format');
    }

    const roomTypes: IUnitType[] = await UnitType.find({ propertyId });
    ResponseUtil.success(res, 'Room types retrieved successfully', roomTypes);
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const roomType = await UnitType.findById(req.params.id);
    if (!roomType) {
      return ResponseUtil.notFound(res, 'Room type not found');
    }
    ResponseUtil.success(res, 'Room type retrieved successfully', roomType);
  });

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const propertyId = req.params.propertyId;
    const property = await PropertyModel.findById(propertyId).populate<{
      serviceType: IDomainValue;
    }>('serviceType');
    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }
    const serviceTypeObj = property.serviceType;

    const unitType: IUnitType = req.body;
    const existingUnitType = await UnitType.findOne({
      propertyId,
      name: unitType.name,
    });
    if (existingUnitType) {
      return ResponseUtil.conflict(res, 'Room type already exists');
    }

    const newRoomType = new UnitType({
      ...unitType,
      propertyId,
      serviceTypeId: serviceTypeObj._id,
    });

    await newRoomType.save();
    ResponseUtil.created(res, 'Room type created successfully', newRoomType);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const existingRoomType = await UnitType.findOne({
      name: req.body.name,
      _id: { $ne: req.params.id },
      propertyId: req.params.propertyId,
    });

    if (existingRoomType) {
      return ResponseUtil.conflict(res, 'Room type name already exists');
    }

    const updatedRoomType = await UnitType.findByIdAndUpdate(req.params.id, req.body, { new: true });

    ResponseUtil.success(res, 'Room type updated successfully', updatedRoomType);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const roomType = await UnitType.findByIdAndDelete(req.params.id);

    if (!roomType) {
      return ResponseUtil.notFound(res, 'Room type not found');
    }

    ResponseUtil.success(res, 'Room type deleted successfully', null, 204);
  });

  updateStatus = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const roomType = await UnitType.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!roomType) {
      return ResponseUtil.notFound(res, 'Room type not found');
    }
    ResponseUtil.success(res, 'Room type status updated successfully', roomType);
  });
}
