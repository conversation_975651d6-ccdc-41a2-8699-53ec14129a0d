import { IPayment, IRequest, IReservation, IResponse, PaymentStatusEnum, ReservationStatusEnum } from '../types';
import PaymentModel from '../models/payment.model';
import { createCheckoutSession, createStripeRefund, getSessionDetails } from '../services/stripe.service';
import Reservation from '../models/reservation.model';
import ReservationItem from '../models/reservation-item.model';
import { EmailService } from '../services/email.service';
import Property from '../models/property.model';
import { validatePayment, validateRefundInputs } from '../utils/cancellation';
import PropertyPoliciesModel from '../models/property-policies.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class PaymentController {
  private static instance: PaymentController;
  private emailService: EmailService;

  private constructor() {
    this.emailService = new EmailService();
  }

  public static getInstance(): PaymentController {
    if (!PaymentController.instance) {
      PaymentController.instance = new PaymentController();
    }
    return PaymentController.instance;
  }

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const payment = await PaymentModel.findById(req.params.id);
    if (!payment) {
      return ResponseUtil.notFound(res, 'Payment not found');
    }
    ResponseUtil.success(res, 'Payment retrieved successfully', payment);
  });

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const reservation = await Reservation.findById(req.body.reservationId);
    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }
    const payment = new PaymentModel({ amount: reservation.grandTotal, ...req.body });
    await payment.save();
    ResponseUtil.created(res, 'Payment created successfully', payment);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const updatedPayment = await PaymentModel.findByIdAndUpdate(req.params.id, req.body, { new: true });

    if (!updatedPayment) {
      return ResponseUtil.notFound(res, 'Payment not found');
    }

    ResponseUtil.success(res, 'Payment updated successfully', updatedPayment);
  });

  verifyPayment = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const payment = await PaymentModel.findById(req.params.id);

    if (!payment) {
      return ResponseUtil.notFound(res, 'Payment not found');
    }

    let reservation;

    if (
      payment.status === PaymentStatusEnum.PAID ||
      payment.status === PaymentStatusEnum.REFUNDED ||
      payment.status === PaymentStatusEnum.FAILED
    ) {
      reservation = await Reservation.findById(payment.reservationId).populate('items');
      if (!reservation) {
        return ResponseUtil.notFound(res, 'Reservation not found');
      }
      return ResponseUtil.success(res, `Payment already marked as ${payment.status}`, { reservation });
    }

    const session = await getSessionDetails(payment.paymentGatewayResponse?.sessionId as string);

    switch (session.payment_status) {
      case 'paid':
        await PaymentModel.findByIdAndUpdate(req.params.id, { status: PaymentStatusEnum.PAID }, { new: true });
        reservation = await Reservation.findByIdAndUpdate(
          payment.reservationId,
          {
            status: ReservationStatusEnum.CONFIRMED,
            paymentStatus: PaymentStatusEnum.PAID,
          },
          { new: true },
        ).populate('items');

        if (reservation) {
          await ReservationItem.updateMany(
            { _id: { $in: reservation.items } },
            { status: ReservationStatusEnum.CONFIRMED, paymentStatus: PaymentStatusEnum.PAID },
          );
          const property = await Property.findById(payment?.propertyId);
          const propertyPolicies = await PropertyPoliciesModel.findOne({ propertyId: reservation.propertyId });
          if (property && propertyPolicies) {
            await this.emailService.sendReservationConfirmationEmail(
              reservation.bookerDetails.email,
              reservation.bookerDetails.firstName,
              reservation,
              property,
              propertyPolicies.cancellationPolicies,
            );
          }
        }
        ResponseUtil.success(res, `Payment marked as ${PaymentStatusEnum.PAID}`, { reservation });
        break;

      case 'unpaid':
        await PaymentModel.findByIdAndUpdate(req.params.id, { status: PaymentStatusEnum.FAILED }, { new: true });
        reservation = await Reservation.findByIdAndUpdate(
          payment.reservationId,
          {
            status: ReservationStatusEnum.CANCELLED,
            paymentStatus: PaymentStatusEnum.FAILED,
          },
          { new: true },
        );
        if (reservation) {
          await ReservationItem.updateMany(
            { _id: { $in: reservation.items } },
            { status: ReservationStatusEnum.CANCELLED, paymentStatus: PaymentStatusEnum.FAILED },
          );
        }
        ResponseUtil.success(res, 'Payment failed', { reservation });
        break;

      default:
        ResponseUtil.badRequest(res, 'Invalid Stripe session status');
        return;
    }
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const payment = await PaymentModel.findByIdAndDelete(req.params.id);

    if (!payment) {
      return ResponseUtil.notFound(res, 'Payment not found');
    }

    ResponseUtil.success(res, 'Payment deleted successfully', null, 204);
  });

  checkout = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const property = await Property.findById(req.body.propertyId);

    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    const reservation = await Reservation.findById(req.body.reservationId).populate('items');

    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }

    const payment = await PaymentModel.create({
      reservationId: req.body.reservationId,
      propertyId: req.body.propertyId,
      amount: reservation.grandTotal,
      currency: req.body.currency,
      paymentMethod: req.body.paymentMethod,
    });

    if (req.params.paymentProvider === 'stripe') {
      const response = await createCheckoutSession(req.body.currency, payment, reservation as unknown as IReservation);
      await PaymentModel.findByIdAndUpdate(payment._id, {
        paymentGateway: 'stripe',
        paymentGatewayResponse: response,
      });
      ResponseUtil.success(res, 'Checkout session created successfully', response);
    }
  });

  refund = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { reservationId, paymentProvider } = req.params;
    const { cancellationTime } = req.query;

    const validationResult = await validateRefundInputs(reservationId, cancellationTime as string, res);
    if (!validationResult) return;

    const { refundPolicy, refundableAmount } = validationResult;

    if (refundPolicy.refund_percent <= 0) {
      // await this.emailService.sendCancellationEmail(reservation.bookerDetails.email, reservation, property);
      ResponseUtil.success(res, 'Refund information retrieved', {
        refundableAmount: 0,
        refundablePercentage: 0,
        description: refundPolicy.description,
      });
      return;
    }

    const payment = await validatePayment(reservationId, res);
    if (!payment) return;

    if (paymentProvider === 'stripe') {
      await this.processStripeRefund(payment, refundableAmount, reservationId, res);
    }
  });

  async processStripeRefund(
    payment: IPayment,
    refundableAmount: number,
    reservationId: string,
    res: IResponse,
  ): Promise<boolean> {
    const sessionId = payment.paymentGatewayResponse?.sessionId;
    if (!sessionId) {
      ResponseUtil.badRequest(res, 'No Payment Info Found');
      return false;
    }

    const refundData = await createStripeRefund(sessionId as string, refundableAmount);
    await PaymentModel.findByIdAndUpdate(payment._id, {
      status: 'refunded',
      refundResponse: { ...refundData, amount: refundableAmount },
    });

    const reservation = await Reservation.findByIdAndUpdate(
      reservationId,
      {
        $set: {
          paymentStatus: 'refunded',
          status: 'cancelled',
          refundAmount: refundableAmount,
          cancellationReason: 'Refunded',
        },
      },
      { new: true },
    );

    if (!reservation) {
      ResponseUtil.notFound(res, 'Reservation not found');
      return false;
    }

    await ReservationItem.updateMany(
      { _id: { $in: reservation.items } },
      { status: ReservationStatusEnum.CANCELLED, paymentStatus: PaymentStatusEnum.REFUNDED },
    );

    // await this.emailService.sendCancellationEmail(reservation.bookerDetails.email, reservation, property);

    ResponseUtil.success(res, 'Refund processed successfully', { reservation });
    return true;
  }
}
