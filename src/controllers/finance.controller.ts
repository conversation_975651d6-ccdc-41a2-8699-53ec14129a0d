import { ICompletedPayouts, IRequest, IResponse } from '../types';
import ReservationModel from '../models/reservation.model';
import mongoose, { PipelineStage } from 'mongoose';
import CompletedPayoutSchema from '../models/completed-payout.model';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class FinanceController {
  private static instance: FinanceController;

  private constructor() {}

  public static getInstance(): FinanceController {
    if (!FinanceController.instance) {
      FinanceController.instance = new FinanceController();
    }
    return FinanceController.instance;
  }

  getDayWiseRevenue = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { start, end, propertyId, q } = req.query;

    if (!start || !end) {
      return ResponseUtil.badRequest(res, 'Please provide both start and end dates');
    }

    const startDate = new Date(start as string);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(end as string);
    endDate.setHours(23, 59, 59, 999);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return ResponseUtil.badRequest(res, 'Invalid date format');
    }

    const escapedQ = q ? String(q).replace(/[.*+?^${}()|[\]\\]/g, '\\$&') : null;

    const pipeline: PipelineStage[] = [
      {
        $match: {
          ...(propertyId ? { propertyId: { $eq: propertyId as string } } : {}),
          status: 'confirmed',
          paymentStatus: 'paid',
          createdAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $lookup: {
          from: 'Property',
          let: { propertyId: '$propertyId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$_id', '$$propertyId'] }, { $eq: ['$active', true] }, { $eq: ['$deleted', false] }],
                },
              },
            },
          ],
          as: 'propertyDetails',
        },
      },
      {
        $unwind: {
          path: '$propertyDetails',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'Location',
          localField: 'propertyDetails.address.locationId',
          foreignField: '_id',
          as: 'locationDetails',
        },
      },
      {
        $unwind: {
          path: '$locationDetails',
          preserveNullAndEmptyArrays: true,
        },
      },
      ...(escapedQ
        ? [
            {
              $match: {
                $or: [
                  {
                    'propertyDetails.name': {
                      $regex: escapedQ,
                      $options: 'i',
                    },
                  },
                  {
                    'propertyDetails.poc.email': {
                      $regex: escapedQ,
                      $options: 'i',
                    },
                  },
                  {
                    'locationDetails.code': {
                      $regex: escapedQ,
                      $options: 'i',
                    },
                  },
                ],
              },
            },
          ]
        : []),
      {
        $group: {
          _id: {
            date: {
              $dateToString: {
                format: '%d-%m-%Y',
                date: '$createdAt',
              },
            },
            propertyId: '$propertyId',
            propertyName: '$propertyDetails.name',
            poc: {
              email: '$propertyDetails.poc.email',
            },
            airport: '$locationDetails.code',
            commissionPercentage: {
              $ifNull: ['$propertyDetails.customFields.businessDetails.commission.percentage', 0],
            },
          },
          totalBooking: { $sum: 1 },
          totalRevenue: { $sum: '$grandTotal' },
          totalTax: { $sum: '$totalTax' },
          totalCancellationAmount: {
            $sum: { $ifNull: ['$cancellationAmount', 0] },
          },
        },
      },
      {
        $project: {
          _id: 0,
          date: '$_id.date',
          propertyId: '$_id.propertyId',
          name: { $ifNull: ['$_id.propertyName', 'Unknown'] },
          email: { $ifNull: ['$_id.poc.email', 'N/A'] },
          airport: { $ifNull: ['$_id.airport', ''] },
          serviceType: { $literal: 'HOTEL' },
          totalBooking: '$totalBooking',
          grandTotal: {
            $round: [{ $subtract: ['$totalRevenue', '$totalCancellationAmount'] }, 2],
          },
          tax: { $round: ['$totalTax', 2] },
          netRevenue: {
            $round: [
              {
                $subtract: [{ $subtract: ['$totalRevenue', '$totalTax'] }, '$totalCancellationAmount'],
              },
              2,
            ],
          },
          commission: {
            $round: [
              {
                $multiply: [
                  {
                    $subtract: [
                      {
                        $subtract: ['$totalRevenue', '$totalCancellationAmount'],
                      },
                      '$totalTax',
                    ],
                  },
                  {
                    $divide: [
                      {
                        $toDouble: {
                          $ifNull: ['$_id.commissionPercentage', 0],
                        },
                      },
                      100,
                    ],
                  },
                ],
              },
              2,
            ],
          },
          netToPay: {
            $round: [
              {
                $subtract: [
                  {
                    $subtract: [
                      {
                        $subtract: ['$totalRevenue', '$totalCancellationAmount'],
                      },
                      '$totalTax',
                    ],
                  },
                  {
                    $multiply: [
                      {
                        $subtract: [
                          {
                            $subtract: ['$totalRevenue', '$totalCancellationAmount'],
                          },
                          '$totalTax',
                        ],
                      },
                      {
                        $divide: [
                          {
                            $toDouble: {
                              $ifNull: ['$_id.commissionPercentage', 0],
                            },
                          },
                          100,
                        ],
                      },
                    ],
                  },
                ],
              },
              2,
            ],
          },
        },
      },
      {
        $sort: {
          date: 1,
          propertyId: 1,
        },
      },
    ];

    const payments = await ReservationModel.aggregate(pipeline).exec();

    ResponseUtil.success(res, 'Day-wise revenue retrieved successfully', payments);
  });

  createCompletedPayout = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId, netPayout, date, mode, referenceNumber, attachment, payoutUntil } = req.body;

    const payout = new CompletedPayoutSchema({
      propertyId,
      netPayout,
      date,
      mode,
      referenceNumber,
      attachment,
      payoutUntil,
    });

    await payout.save();
    ResponseUtil.created(res, 'Payout created successfully', payout);
  });

  getAllCompletedPayouts = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { q, start, end } = req.query;

    const matchStage: mongoose.FilterQuery<ICompletedPayouts> = {};

    const startDate = new Date(start as string);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(end as string);
    endDate.setHours(23, 59, 59, 999);

    if (start || end) {
      matchStage.date = {};
      if (start) {
        matchStage.date.$gte = startDate;
      }
      if (end) {
        matchStage.date.$lte = endDate;
      }
    }

    const pipeline: PipelineStage[] = [
      { $match: matchStage },
      {
        $lookup: {
          from: 'Property',
          localField: 'propertyId',
          as: 'propertyId',
          foreignField: '_id',
        },
      },
      {
        $unwind: { path: '$propertyId', preserveNullAndEmptyArrays: true },
      },
      {
        $lookup: {
          from: 'Location',
          localField: 'propertyId.address.locationId',
          foreignField: '_id',
          as: 'propertyId.address.locationId',
        },
      },
      {
        $unwind: {
          path: '$propertyId.address.locationId',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $set: {
          'propertyId.customFields.serviceType': {
            $cond: {
              if: {
                $eq: [{ $type: '$propertyId.customFields.serviceType' }, 'string'],
              },
              then: { $toObjectId: '$propertyId.customFields.serviceType' },
              else: '$propertyId.customFields.serviceType',
            },
          },
        },
      },
      {
        $lookup: {
          from: 'DomainValue',
          localField: 'propertyId.customFields.serviceType',
          foreignField: '_id',
          as: 'propertyId.customFields.serviceType',
          pipeline: [
            {
              $project: {
                _id: 0,
                name: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$propertyId.customFields.serviceType',
          preserveNullAndEmptyArrays: true,
        },
      },
    ];

    if (q) {
      const searchRegex = { $regex: q as string, $options: 'i' };
      pipeline.push({
        $match: {
          $or: [
            { 'propertyId.name': searchRegex },
            { 'propertyId.poc.email': searchRegex },
            { 'propertyId.poc.firstName': searchRegex },
            { 'propertyId.poc.lastName': searchRegex },
            { 'propertyId.address.location.name': searchRegex },
            { 'propertyId.address.location.code': searchRegex },
          ],
        },
      });
    }

    const payouts = await CompletedPayoutSchema.aggregate(pipeline);

    ResponseUtil.success(res, 'Completed payouts retrieved successfully', payouts);
  });

  getConsolidatedRevenueByDates = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { start, end, propertyId, q } = req.query;

    if (!start || !end) {
      return ResponseUtil.badRequest(res, 'Please provide both start and end dates');
    }

    const startDate = new Date(start as string);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(end as string);
    endDate.setHours(23, 59, 59, 999);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return ResponseUtil.badRequest(res, 'Invalid date format');
    }

    const escapedQ = q ? String(q).replace(/[.*+?^${}()|[\]\\]/g, '\\$&') : null;

    const pipeline: PipelineStage[] = [
      {
        $match: {
          ...(propertyId ? { propertyId: { $eq: propertyId as string } } : {}),
          status: 'confirmed',
          paymentStatus: 'paid',
          createdAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $lookup: {
          from: 'Property',
          let: { propertyId: '$propertyId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$_id', '$$propertyId'] }, { $eq: ['$active', true] }, { $eq: ['$deleted', false] }],
                },
              },
            },
          ],
          as: 'propertyDetails',
        },
      },
      {
        $unwind: {
          path: '$propertyDetails',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'Location',
          localField: 'propertyDetails.address.locationId',
          foreignField: '_id',
          as: 'locationDetails',
        },
      },
      {
        $unwind: {
          path: '$locationDetails',
          preserveNullAndEmptyArrays: true,
        },
      },
      ...(escapedQ
        ? [
            {
              $match: {
                $or: [
                  {
                    'propertyDetails.name': {
                      $regex: escapedQ,
                      $options: 'i',
                    },
                  },
                  {
                    'propertyDetails.poc.email': {
                      $regex: escapedQ,
                      $options: 'i',
                    },
                  },
                  {
                    'locationDetails.code': {
                      $regex: escapedQ,
                      $options: 'i',
                    },
                  },
                ],
              },
            },
          ]
        : []),
      {
        $group: {
          _id: {
            propertyId: '$propertyId',
            propertyName: '$propertyDetails.name',
            poc: {
              email: '$propertyDetails.poc.email',
            },
            airport: '$locationDetails.code',
            commissionPercentage: {
              $ifNull: ['$propertyDetails.customFields.businessDetails.commission.percentage', 0],
            },
          },
          totalBooking: { $sum: 1 },
          totalRevenue: { $sum: '$grandTotal' },
          totalTax: { $sum: '$totalTax' },
          totalCancellationAmount: {
            $sum: { $ifNull: ['$cancellationAmount', 0] },
          },
        },
      },
      {
        $project: {
          _id: 0,
          propertyId: '$_id.propertyId',
          name: { $ifNull: ['$_id.propertyName', 'Unknown'] },
          email: { $ifNull: ['$_id.poc.email', 'N/A'] },
          airport: { $ifNull: ['$_id.airport', ''] },
          serviceType: { $literal: 'HOTEL' },
          totalBooking: '$totalBooking',
          grandTotal: {
            $round: [{ $subtract: ['$totalRevenue', '$totalCancellationAmount'] }, 2],
          },
          tax: { $round: ['$totalTax', 2] },
          netRevenue: {
            $round: [
              {
                $subtract: [{ $subtract: ['$totalRevenue', '$totalTax'] }, '$totalCancellationAmount'],
              },
              2,
            ],
          },
          commission: {
            $round: [
              {
                $multiply: [
                  {
                    $subtract: ['$totalRevenue', '$totalCancellationAmount'],
                  },
                  {
                    $divide: [
                      {
                        $toDouble: {
                          $ifNull: ['$_id.commissionPercentage', 0],
                        },
                      },
                      100,
                    ],
                  },
                ],
              },
              2,
            ],
          },
          netToPay: {
            $round: [
              {
                $subtract: [
                  {
                    $subtract: [
                      {
                        $subtract: ['$totalRevenue', '$totalCancellationAmount'],
                      },
                      '$totalTax',
                    ],
                  },
                  {
                    $multiply: [
                      {
                        $subtract: [
                          {
                            $subtract: ['$totalRevenue', '$totalCancellationAmount'],
                          },
                          '$totalTax',
                        ],
                      },
                      {
                        $divide: [
                          {
                            $toDouble: {
                              $ifNull: ['$_id.commissionPercentage', 0],
                            },
                          },
                          100,
                        ],
                      },
                    ],
                  },
                ],
              },
              2,
            ],
          },
        },
      },
      {
        $sort: {
          propertyId: 1,
        },
      },
    ];

    const revenue = await ReservationModel.aggregate(pipeline).exec();

    ResponseUtil.success(res, 'Consolidated revenue retrieved successfully', revenue);
  });

  getNetPayoutUntilDate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId, date } = req.query;
    if (!propertyId) {
      return ResponseUtil.badRequest(res, 'Please provide property');
    }

    if (!date) {
      return ResponseUtil.badRequest(res, 'Please provide until date');
    }

    const startDate = new Date(date as string);
    startDate.setHours(0, 0, 0, 0);

    const payoutQuery = {
      propertyId: propertyId,
      date: {
        $lte: startDate,
      },
    };

    const payouts = await CompletedPayoutSchema.find(payoutQuery);

    const reservationQuery = {
      propertyId: propertyId,
      createdAt: {
        $lte: startDate,
      },
    };

    const reservations = await ReservationModel.find(reservationQuery);

    const totalPayout = payouts.reduce((acc, payout) => acc + payout.netPayout, 0);
    const totalReservation = reservations.reduce((acc: number, reservation) => acc + reservation.grandTotal, 0);

    const totalRefund = reservations.reduce((acc, reservation) => acc + (reservation.refundAmount || 0), 0);

    const netPayout = totalReservation - totalPayout - totalRefund;

    ResponseUtil.success(res, 'Net payout retrieved successfully', { netPayout: netPayout > 0 ? netPayout : 0 });
  });
}
