import { ILocation, IPackage, IProperty, IRequest, IResponse, IUnitType, IUser } from '../types';
import Property from '../models/property.model';
import Package from '../models/package.model';
import Location from '../models/location.model';
import { EmailService } from '../services/email.service';
import mongoose from 'mongoose';
import UnitTypeModel from '../models/unit-type.model';
import { checkRoomAvailability } from '../utils/availability';
import { getRateCardPrices } from '../utils/rateCard';
import { toUTCDate } from '../utils/date-utils';
import { ResponseUtil } from '../utils/response';
import PropertyDetailsModel from '../models/property-details.model';
import BusinessDetailsModel from '../models/business-details.model';
import PropertyPoliciesModel from '../models/property-policies.model';
import { asyncRequestHandler } from '../middlewares/async-request-handler';
import loggerService from '../utils/logger/logger.service';

export class PropertyController {
  private static instance: PropertyController;
  private emailService: EmailService;

  private constructor() {
    this.emailService = new EmailService();
  }

  public static getInstance(): PropertyController {
    if (!PropertyController.instance) {
      PropertyController.instance = new PropertyController();
    }
    return PropertyController.instance;
  }

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const propertyFilterQuery: mongoose.FilterQuery<IProperty> = {};
    const city = req.query.city as string | undefined;
    const locationId = req.query.locationId as string | undefined;

    const active = req.query.active as string | undefined;

    if (locationId) {
      const location = await Location.findById(locationId);
      if (location) {
        const allCityLocations: ILocation[] = await Location.find({
          city: location.city,
        });

        propertyFilterQuery['address.locationId'] = {
          $in: allCityLocations.map((l) => l._id),
        };
      }
    } else if (city) {
      propertyFilterQuery['address.city'] = city;
    }

    if (active === 'true') {
      propertyFilterQuery['active'] = true;
    }

    const properties: (IProperty & { packages?: IPackage[] })[] = await Property.find(propertyFilterQuery)
      .lean()
      .sort({ updatedAt: -1 })
      .populate([
        {
          path: 'address.locationId',
        },
        {
          path: 'serviceType',
        },
        {
          path: 'owner',
          select: '-password -createdAt -updatedAt -__v -role',
        },
      ]);

    if (req.query.includesPackages === 'true') {
      const { duration, noOfAdults, noOfChildren, startDateTime, endDateTime } = req.query;

      const packageFilter: mongoose.FilterQuery<IPackage> = {
        propertyId: { $in: properties.map((p) => p._id) },
      };

      if (duration) {
        packageFilter.duration = { $gte: parseInt(duration as string, 10) };
      }

      if (noOfAdults) {
        packageFilter.noOfAdults = parseInt(noOfAdults as string, 10);
      }

      if (noOfChildren) {
        packageFilter.noOfChildren = parseInt(noOfChildren as string, 10);
      }

      if (startDateTime && endDateTime) {
        const start = toUTCDate(startDateTime as string);
        const end = toUTCDate(endDateTime as string);
        packageFilter.duration = {
          $gte: Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60)),
        };
      }

      let packages = await Package.find(packageFilter).populate('unitTypeId').populate('taxes').lean();

      if (startDateTime && endDateTime) {
        const startDate = toUTCDate(startDateTime as string);
        const endDate = toUTCDate(endDateTime as string);

        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          const uniqueRoomTypes = Array.from(new Set(packages.map((pkg) => pkg.unitTypeId as unknown as IUnitType)));

          // Modified to handle each propertyId individually
          const availabilityResults = await Promise.all(
            properties.map(async (property) => {
              return await checkRoomAvailability({
                propertyId: property._id.toString(),
                roomTypes: uniqueRoomTypes,
                startDateTime: startDateTime as string,
                endDateTime: endDateTime as string,
              });
            }),
          );

          // Flatten and create availability map
          const availabilityMap = new Map(
            availabilityResults.flat().map((result) => [result.unitTypeId, result.availableRooms]),
          );

          const packageIds = packages.map((pkg) => pkg._id.toString());
          const rateCardMap = await getRateCardPrices({
            packageIds,
            startDateTime: startDateTime as string,
            endDateTime: endDateTime as string,
          });

          packages = packages
            .map((pkg) => {
              const rtId = (pkg.unitTypeId as unknown as IUnitType)._id.toString();
              const availableRooms = availabilityMap.get(rtId) || 0;

              return {
                ...pkg,
                rateCardPrice: rateCardMap[pkg._id.toString()] !== undefined ? rateCardMap[pkg._id.toString()] : null,
                available: availableRooms,
              };
            })
            .filter((pkg) => pkg.available > 0);
        } else {
          packages = packages.map((pkg) => ({
            ...pkg,
            rateCardPrice: null,
            available: (pkg.unitTypeId as unknown as IUnitType).totalUnits,
          }));
        }
      }

      properties.forEach((property) => {
        property.packages = packages.filter((pkg) => property._id.toString() === pkg.propertyId.toString());
      });
    }

    ResponseUtil.success(res, 'Properties retrieved successfully', properties);
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const property = await Property.findById(req.params.id).populate([
      {
        path: 'address.locationId',
      },
      {
        path: 'serviceType',
      },
      {
        path: 'owner',
        select: '-password -createdAt -updatedAt -__v -role',
      },
    ]);
    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }
    ResponseUtil.success(res, 'Property retrieved successfully', property);
  });

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const userId = req.user?.userId;
    if (!userId) {
      return ResponseUtil.unauthorized(res, 'Unauthorized');
    }
    const property: IProperty = req.body;
    const existingProperty = await Property.findOne({
      name: property.name,
    });
    if (existingProperty) {
      return ResponseUtil.conflict(res, 'Property already exists');
    }

    const newProperty = new Property({
      ...property,
      owner: userId,
    });

    await newProperty.save();

    await newProperty.populate([
      {
        path: 'owner',
      },
    ]);

    const poc = newProperty.owner as unknown as IUser;

    await this.emailService.sendApprovalStatusEmail(poc.email, poc.firstName, property, 'Pending');
    ResponseUtil.created(res, 'Property created successfully', newProperty);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const property = await Property.findById(req.params.id);
    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    const existingProperty = await Property.findOne({
      name: req.body.name,
      _id: { $ne: req.params.id },
    });

    if (existingProperty) {
      return ResponseUtil.conflict(res, 'Property name already exists');
    }

    const updatedProperty = await Property.findByIdAndUpdate(req.params.id, req.body, { new: true });

    ResponseUtil.success(res, 'Property updated successfully', updatedProperty);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const property = await Property.findByIdAndDelete(req.params.id);

    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    ResponseUtil.success(res, 'Property deleted successfully', null, 204);
  });

  patchStatus = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { status, active } = req.body;

    // Get the current property to compare status changes
    const currentProperty = await Property.findById(req.params.id);
    if (!currentProperty) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    const property = await Property.findByIdAndUpdate(req.params.id, { status, active }, { new: true }).populate([
      {
        path: 'owner',
      },
    ]);

    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    // Send email notifications for status changes
    const poc = property.owner as unknown as IUser;
    try {
      // Check if approval status changed
      if (status && status !== currentProperty.status && (status === 'Approved' || status === 'Rejected')) {
        await this.emailService.sendApprovalStatusEmail(poc.email, poc.firstName, property, status);
      }

      // Check if active status changed
      if (active !== undefined && active !== currentProperty.active) {
        await this.emailService.sendActiveStatusEmail(poc.email, poc.firstName, property, active);
      }
    } catch (emailError) {
      loggerService.error(`Failed to send email`, 'property.controller.ts', emailError);
      // Don't fail the request if email fails
    }

    ResponseUtil.success(res, 'Property status updated successfully', property);
  });

  hasData = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const property = await Property.findById(req.params.id);
    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    const [unitTypesCount, propertyDetails, businessDetailsCount, propertyPoliciesCount] = await Promise.all([
      UnitTypeModel.countDocuments({ propertyId: req.params.id }),
      PropertyDetailsModel.findOne({ propertyId: req.params.id }).lean(),
      BusinessDetailsModel.countDocuments({ propertyId: req.params.id }),
      PropertyPoliciesModel.countDocuments({ propertyId: req.params.id }),
    ]);

    const hasData = {
      hasUnitTypes: unitTypesCount > 0,
      hasPropertyDetails: !!propertyDetails,
      hasBusinessDetails: businessDetailsCount > 0,
      hasPropertyPolicies: propertyPoliciesCount > 0,
      hasAmenities: !!(
        propertyDetails &&
        Array.isArray(propertyDetails.amenities) &&
        propertyDetails.amenities.length > 0
      ),
    };

    ResponseUtil.success(res, 'Property data check successful', hasData);
  });
}
