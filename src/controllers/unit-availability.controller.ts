import { IRequest, IResponse, IRoomAvailability } from '../types';
import RoomAvailabilityModel from '../models/unit-availability.model';
import RoomTypeModel from '../models/unit-type.model';
import { toUTCDate } from '../utils/date-utils';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class UnitAvailabilityController {
  private static Instance: UnitAvailabilityController;

  public static getInstance(): UnitAvailabilityController {
    if (!UnitAvailabilityController.Instance) {
      UnitAvailabilityController.Instance = new UnitAvailabilityController();
    }
    return UnitAvailabilityController.Instance;
  }

  private constructor() {}

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const roomAvailabilities = req.body.roomAvailabilities;

    const roomTypes = await RoomTypeModel.find({
      _id: {
        $in: roomAvailabilities.map((ra: IRoomAvailability) => ra.roomType),
      },
    });

    // Collect validation errors
    const validationErrors: string[] = [];
    roomAvailabilities.forEach((ra: IRoomAvailability) => {
      const roomType = roomTypes.find((rt) => rt._id.toString() === ra.roomType.toString());
      if (!roomType) {
        validationErrors.push(`Room type ${ra.roomType} not found`);
      } else if (roomType.totalUnits < ra.availability) {
        validationErrors.push(`Availability cannot be greater than no of rooms for room type ${roomType.name}`);
      }
    });

    // If there are any validation errors, return them all at once
    if (validationErrors.length > 0) {
      return ResponseUtil.badRequest(res, 'Validation errors', validationErrors.join(', '));
    }

    // Prepare bulk operations with upsert
    const bulkOperations = roomAvailabilities.map((roomAvailability: IRoomAvailability) => ({
      updateOne: {
        filter: {
          roomType: roomAvailability.roomType,
          dateTime: roomAvailability.dateTime,
          propertyId: req.params.propertyId,
        },
        update: { $set: roomAvailability },
        upsert: true,
      },
    }));

    const result = await RoomAvailabilityModel.bulkWrite(bulkOperations);

    ResponseUtil.success(res, 'Room availability created/updated successfully', result);
  });

  getByDate = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { date } = req.query;
    const { propertyId } = req.params;

    const startDate = toUTCDate(date as string);
    startDate.setHours(0, 0, 0, 0);

    const endDate = toUTCDate(date as string);
    endDate.setHours(23, 59, 59, 999);

    if (!date) {
      return ResponseUtil.badRequest(res, 'Please provide date');
    }

    const query = {
      propertyId: propertyId,
      dateTime: {
        $gte: startDate,
        $lte: endDate,
      },
    };

    const roomAvailabilities = await RoomAvailabilityModel.find(query).populate('roomType');

    ResponseUtil.success(res, 'Room availabilities retrieved successfully', roomAvailabilities);
  });
}
