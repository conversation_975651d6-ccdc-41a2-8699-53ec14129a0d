import { ILocation, IRequest, IResponse } from '../types';
import Location from '../models/location.model';
import axios from 'axios';
import { countryCodes } from '../utils/countryCodes';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class LocationController {
  private static Instance: LocationController;

  public static getInstance(): LocationController {
    if (!LocationController.Instance) LocationController.Instance = new LocationController();
    return LocationController.Instance;
  }

  private constructor() {}

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { search } = req.query;
    let locations: ILocation[];

    if (search) {
      const searchRegex = new RegExp(String(search), 'i');
      locations = await Location.find({
        $or: [{ code: searchRegex }, { name: searchRegex }, { city: searchRegex }],
        deleted: false,
      }).sort({
        updatedAt: -1,
      });
    } else {
      locations = await Location.find({ deleted: false }).sort({
        updatedAt: -1,
      });
    }

    ResponseUtil.success(res, 'Locations retrieved successfully', locations);
  });

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const location = await Location.create(req.body);
    ResponseUtil.created(res, 'Location created successfully', location);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { id } = req.params;
    const location = await Location.findByIdAndUpdate(id, req.body, {
      new: true,
    });
    if (!location) {
      return ResponseUtil.notFound(res, 'Location not found');
    }
    ResponseUtil.success(res, 'Location updated successfully', location);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { id } = req.params;
    const location = await Location.findByIdAndUpdate(id, { deleted: true });
    if (!location) {
      return ResponseUtil.notFound(res, 'Location not found');
    }
    ResponseUtil.success(res, 'Location deleted successfully');
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const location = await Location.findById(req.params.id);
    if (!location) {
      return ResponseUtil.notFound(res, 'Location not found');
    }
    ResponseUtil.success(res, 'Location retrieved successfully', location);
  });

  getLocationDetailsByGoogleMapsApi = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { latitude, longitude } = req.query;

    // // Validate required parameters
    if (!latitude || !longitude) {
      return ResponseUtil.badRequest(res, 'Latitude and longitude are required');
    }

    // // Validate that coordinates are valid numbers
    const lat = parseFloat(String(latitude));
    const lng = parseFloat(String(longitude));

    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${process.env.GOOGLE_MAPS_API_KEY}`,
    );

    // Extract country code from the response
    let countryCode = null;
    if (response.data.results && response.data.results.length > 0) {
      const addressComponents = response.data.results[0].address_components;
      const countryComponent = addressComponents.find((component: { types: string[] }) =>
        component.types.includes('country'),
      );
      if (countryComponent) {
        countryCode = countryComponent.short_name;
      }
    }

    // Return the original response data along with the extracted country code
    const locationData = {
      ...response.data,
      countryPhoneCode: countryCodes.find((c) => c.iso === countryCode)?.value || null,
    };
    ResponseUtil.success(res, 'Location details retrieved successfully', locationData);
  });
}
