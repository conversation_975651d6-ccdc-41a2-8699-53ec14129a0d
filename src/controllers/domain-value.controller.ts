import { IRequest, IResponse, IDomainValue } from '../types';
import DomainValue from '../models/domain-value.model';
import mongoose from 'mongoose';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class DomainValueController {
  private static instance: DomainValueController;

  public static getInstance(): DomainValueController {
    if (!DomainValueController.instance) {
      DomainValueController.instance = new DomainValueController();
    }
    return DomainValueController.instance;
  }

  private constructor() {}

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId, categoryId, level, parentId } = req.query;
    const query: mongoose.FilterQuery<IDomainValue> = {};

    if (propertyId) {
      query.propertyId = propertyId;
    }

    if (categoryId) {
      query.categoryId = new mongoose.Types.ObjectId(categoryId as string);
    }

    if (level) {
      query.level = level;
    }

    if (parentId) {
      query.parentId = new mongoose.Types.ObjectId(parentId as string);
    }

    const domainValues: IDomainValue[] = await DomainValue.find(query);
    ResponseUtil.success(res, 'Domain values retrieved successfully', domainValues);
  });

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { name, level, parentId, propertyId, categoryId } = req.body;

    const existingDomainValue = await DomainValue.findOne({
      name,
      level,
      parentId,
      propertyId,
      categoryId,
    });
    if (existingDomainValue) {
      return ResponseUtil.conflict(res, 'Domain value already exists');
    }

    const domainValue: IDomainValue = await DomainValue.create(req.body);
    ResponseUtil.created(res, 'Domain value created successfully', domainValue);
  });

  getById = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const domainValue = await DomainValue.findById(req.params.id);
    if (!domainValue) {
      return ResponseUtil.notFound(res, 'Domain value not found');
    }
    ResponseUtil.success(res, 'Domain value retrieved successfully', domainValue);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const domainValue = await DomainValue.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!domainValue) {
      return ResponseUtil.notFound(res, 'Domain value not found');
    }
    ResponseUtil.success(res, 'Domain value updated successfully', domainValue);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const domainValue = await DomainValue.findByIdAndDelete(req.params.id);
    if (!domainValue) {
      return ResponseUtil.notFound(res, 'Domain value not found');
    }
    ResponseUtil.success(res, 'Domain value deleted successfully', domainValue);
  });

  getByLevel = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const domainValue = await DomainValue.find({
      level: req.params.level,
    });
    if (!domainValue || domainValue.length === 0) {
      return ResponseUtil.notFound(res, 'Domain values not found for this level');
    }
    ResponseUtil.success(res, 'Domain values retrieved successfully', domainValue);
  });

  getByCategoryId = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const domainValue = await DomainValue.find({
      categoryId: req.params.categoryId,
    });
    ResponseUtil.success(res, 'Domain values retrieved successfully', domainValue);
  });

  getByCategoryIdAndPropertyId = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const domainValue = await DomainValue.find({
      categoryId: req.params.categoryId,
      propertyId: req.params.propertyId,
    });
    ResponseUtil.success(res, 'Domain values retrieved successfully', domainValue);
  });
}
