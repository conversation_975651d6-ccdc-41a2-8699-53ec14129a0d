import { IPolicy, IRequest, IResponse } from '../types';
import Policy from '../models/policy.model';
import mongoose from 'mongoose';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class PolicyController {
  private static Instance: PolicyController;

  public static getInstance(): PolicyController {
    if (!PolicyController.Instance) {
      PolicyController.Instance = new PolicyController();
    }
    return PolicyController.Instance;
  }

  private constructor() {}

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const policy = await Policy.create(req.body);
    ResponseUtil.created(res, 'Policy template created successfully', policy);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const policy = await Policy.findByIdAndUpdate(req.params.id, req.body, { new: true });

    if (!policy) {
      return ResponseUtil.notFound(res, 'Policy template not found');
    }

    ResponseUtil.success(res, 'Policy template updated successfully', policy);
  });

  getById = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return ResponseUtil.notFound(res, 'Policy template not found');
    }

    ResponseUtil.success(res, 'Policy template fetched successfully', policy);
  });

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId, input_type, is_required, serviceTypeId } = req.query;

    const filter: mongoose.FilterQuery<IPolicy> = { deleted: false };

    if (propertyId) filter.propertyId = propertyId;
    if (input_type) filter.input_type = input_type;
    if (is_required !== undefined) filter.is_required = is_required === 'true';
    if (serviceTypeId) filter.serviceType = serviceTypeId;

    const policies = await Policy.find(filter).populate('category').sort({ category: 1, updatedAt: 1 });

    ResponseUtil.success(res, 'Policies retrieved successfully', policies);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const policy = await Policy.findByIdAndDelete(req.params.id);

    if (!policy) {
      return ResponseUtil.notFound(res, 'Policy template not found');
    }

    ResponseUtil.success(res, 'Policy template deleted successfully');
  });
}
