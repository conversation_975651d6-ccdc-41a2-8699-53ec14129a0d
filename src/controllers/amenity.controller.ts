import AmenityModel from '../models/amenities.model';
import { IAmenity, IRequest, IResponse } from '../types';
import mongoose from 'mongoose';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class AmenityController {
  private static Instance: AmenityController;

  public static getInstance(): AmenityController {
    if (!AmenityController.Instance) {
      AmenityController.Instance = new AmenityController();
    }
    return AmenityController.Instance;
  }

  private constructor() {}

  create = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const amenity = new AmenityModel(req.body);
    await amenity.save();
    ResponseUtil.created(res, 'Amenity created successfully', amenity);
  });

  getAll = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { categoryId, serviceTypeId } = req.query;
    const query: mongoose.FilterQuery<IAmenity> = {};

    if (categoryId) {
      query.categoryId = new mongoose.Types.ObjectId(categoryId as string);
    }
    if (serviceTypeId) {
      query.serviceTypeId = new mongoose.Types.ObjectId(serviceTypeId as string);
    }

    const amenities = await AmenityModel.find(query)
      .populate([
        {
          path: 'categoryId',
        },
        {
          path: 'serviceTypeId',
        },
      ])
      .sort({ updatedAt: 1 });
    ResponseUtil.success(res, 'Amenities retrieved successfully', amenities);
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const amenity = await AmenityModel.findById(req.params.id);
    if (!amenity) {
      return ResponseUtil.notFound(res, 'Amenity not found');
    }
    ResponseUtil.success(res, 'Amenity retrieved successfully', amenity);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const amenity = await AmenityModel.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!amenity) {
      return ResponseUtil.notFound(res, 'Amenity not found');
    }
    ResponseUtil.success(res, 'Amenity updated successfully', amenity);
  });

  delete = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const amenity = await AmenityModel.findByIdAndDelete(req.params.id);
    if (!amenity) {
      return ResponseUtil.notFound(res, 'Amenity not found');
    }
    ResponseUtil.success(res, 'Amenity deleted successfully', amenity);
  });
}
