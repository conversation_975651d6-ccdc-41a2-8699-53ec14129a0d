import Reservation from '../models/reservation.model';
import ReservationItem from '../models/reservation-item.model';
import RoomType from '../models/unit-type.model';
import Package from '../models/package.model';
import mongoose, { Types } from 'mongoose';
import DomainValue from '../models/domain-value.model';
import UserSchema from '../models/user.model';
import { validateRefundInputs } from '../utils/cancellation';
import OTPSchema from '../models/otp-model';
import { generateRandomOTP } from '../utils/otpUtils';
import { EmailService } from '../services/email.service';
import PropertyModel from '../models/property.model';
import { checkRoomAvailability } from '../utils/availability';
import { getRateCardPrices } from '../utils/rateCard';
import {
  IDomainValue,
  IOtp,
  IPackage,
  IRequest,
  IReservation,
  IResponse,
  IUnitType,
  OtpPurposeEnum,
  ReservationStatusEnum,
  UserRoleEnum,
} from '../types';
import { toUTCDate } from '../utils/date-utils';
import { ResponseUtil } from '../utils/response';
import { asyncRequestHandler } from '../middlewares/async-request-handler';

export class ReservationController {
  private static Instance: ReservationController;
  private emailService: EmailService;

  public static getInstance(): ReservationController {
    if (!ReservationController.Instance) {
      ReservationController.Instance = new ReservationController();
    }
    return ReservationController.Instance;
  }

  private constructor() {
    this.emailService = new EmailService();
  }

  getAvailable = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(propertyId)) {
      return ResponseUtil.badRequest(res, 'Invalid property ID format');
    }

    const { duration, noOfAdults, noOfChildren, startDateTime, endDateTime } = req.query;

    const packageFilter: mongoose.FilterQuery<IPackage> = {
      propertyId: propertyId,
    };

    if (duration) {
      packageFilter.duration = { $gte: parseInt(duration as string, 10) };
    }

    if (noOfAdults) {
      packageFilter.noOfAdults = parseInt(noOfAdults as string, 10);
    }

    if (noOfChildren) {
      packageFilter.noOfChildren = parseInt(noOfChildren as string, 10);
    }
    if (startDateTime && endDateTime) {
      const start = toUTCDate(startDateTime as string);
      const end = toUTCDate(endDateTime as string);
      packageFilter.duration = {
        $gte: Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60)),
      };
    }

    const packages: (IPackage & {
      unitTypeId: IUnitType;
      taxes: IDomainValue[];
      amenities: IDomainValue[];
    })[] = await Package.find(packageFilter, {}, { populate: ['unitTypeId', 'amenities', 'taxes'] });

    const packageIds = packages.map((pkg) => pkg._id.toString());
    const rateCardMap = await getRateCardPrices({
      packageIds,
      startDateTime: startDateTime as string,
      endDateTime: endDateTime as string,
    });

    const roomTypePackages = packages.reduce(
      (acc, pkg) => {
        const unitTypeId = pkg.unitTypeId._id.toString();
        if (!acc[unitTypeId]) {
          acc[unitTypeId] = [];
        }
        const updatedPkg = {
          ...pkg.toObject(),
          rateCardPrice: rateCardMap[pkg._id.toString()] !== undefined ? rateCardMap[pkg._id.toString()] : null,
        };
        acc[unitTypeId].push(updatedPkg);
        return acc;
      },
      {} as Record<string, unknown[]>,
    );

    const roomTypes = await RoomType.find({
      propertyId,
      active: true,
    });

    let roomTypesWithPackages = roomTypes.map((roomType) => ({
      ...roomType.toObject(),
      packages: roomTypePackages[roomType._id.toString()] || [],
    }));

    if (startDateTime && endDateTime) {
      const availabilityResults = await checkRoomAvailability({
        propertyId,
        roomTypes,
        startDateTime: startDateTime as string,
        endDateTime: endDateTime as string,
      });

      // Update room types with availability
      roomTypesWithPackages = roomTypesWithPackages
        .map((roomType) => {
          const availabilityResult = availabilityResults.find(
            (result) => result.unitTypeId === roomType._id.toString(),
          );
          const availableRooms = availabilityResult?.availableRooms || 0;

          return {
            ...roomType,
            available: availableRooms,
            packages: roomType.packages.filter(() => availableRooms > 0),
          };
        })
        .filter((rt) => rt.available > 0);
    }

    ResponseUtil.success(res, 'Room availability retrieved successfully', roomTypesWithPackages);
  });

  block = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(propertyId)) {
      return ResponseUtil.badRequest(res, 'Invalid ID format');
    }

    const { reservations, bookerDetails } = req.body;
    if (!reservations || !Array.isArray(reservations) || reservations.length === 0) {
      return ResponseUtil.badRequest(res, 'No reservations provided');
    }

    const packageIds = reservations.map((reservation: Record<string, unknown>) => reservation.packageId);
    if (!packageIds || packageIds.length === 0) {
      return ResponseUtil.badRequest(res, 'No packageId provided');
    }

    const packages = await Package.find({
      _id: { $in: packageIds },
    }).lean();
    const taxIds = packages.flatMap((pkg) => pkg.taxes);
    const taxes = await DomainValue.find({ _id: { $in: taxIds } }).lean();

    if (!packages || packages.length === 0) {
      return ResponseUtil.notFound(res, 'No packages found');
    }

    const unitTypeIds = reservations.map(
      (resp) => resp.unitTypeId || packages.find((pkg) => pkg._id.toString() === resp.packageId.toString())?.unitTypeId,
    );
    const roomTypes = await RoomType.find({ _id: { $in: unitTypeIds } });

    for (const reservation of reservations) {
      const roomType = roomTypes.find((rt) => rt._id.toString() === reservation.unitTypeId?.toString());
      if (!roomType) continue;

      const availabilityResults = await checkRoomAvailability({
        propertyId,
        roomTypes: [roomType],
        startDateTime: reservation.startDateTime.toString(),
        endDateTime: reservation.endDateTime.toString(),
      });

      const availableRooms = availabilityResults[0]?.availableRooms || 0;

      if (availableRooms <= 0) {
        return ResponseUtil.badRequest(res, `No rooms available for ${roomType.name} during the requested period`);
      }
    }

    const rateCardMap = await getRateCardPrices({
      packageIds: packageIds.map((id) => (id as mongoose.Types.ObjectId).toString()),
      startDateTime: reservations[0]?.startDateTime,
      endDateTime: reservations[0]?.endDateTime,
    });

    const createdReservationItems = [];
    let subTotal = 0;
    let totalTax = 0;

    for (const reservationData of reservations) {
      const pkg = packages.find((pkgData) => pkgData._id.toString() === reservationData.packageId.toString());
      const packageTax = taxes.filter((tax) => pkg?.taxes.map((t) => t.toString()).includes(tax._id.toString()));

      const price =
        rateCardMap[reservationData.packageId.toString()] !== undefined
          ? rateCardMap[reservationData.packageId.toString()]
          : pkg?.price || 0;

      const tax = packageTax.reduce((acc: number, taxItem) => {
        const taxValue = Number(taxItem.value) || 0;
        return acc + (price * taxValue) / 100;
      }, 0);

      const newReservationItem = new ReservationItem({
        propertyId: new mongoose.Types.ObjectId(propertyId),
        reservationCode: '0', // Will be generated by pre-save hook
        status: ReservationStatusEnum.BLOCKED,
        paymentStatus: req.body.paymentStatus || 'pending',
        couponDiscount: reservationData.couponDiscount || 0,
        unitTypeId: new Types.ObjectId(pkg?.unitTypeId),
        packageId: new Types.ObjectId(reservationData.packageId),
        startDateTime: new Date(reservationData.startDateTime),
        endDateTime: new Date(reservationData.endDateTime),
        noOfAdults: reservationData.noOfAdults || 1,
        noOfChildren: reservationData.noOfChildren || 0,
        price,
        taxes: packageTax || [],
        tax,
        totalAmount: price + tax,
        guestDetails: reservationData.guestDetails || [],
        specialRequest: reservationData.specialRequest || '',
        flightDetails: reservationData.flightDetails || {},
        bookerDetails,
      });

      await newReservationItem.save();
      createdReservationItems.push(newReservationItem);
      subTotal += price;
      totalTax += tax;
    }

    const newReservation = new Reservation({
      propertyId: new mongoose.Types.ObjectId(propertyId),
      items: createdReservationItems.map((item) => item._id),
      subTotal,
      totalTax,
      grandTotal: subTotal + totalTax,
      bookerDetails,
    });

    await newReservation.save();

    ResponseUtil.created(res, 'Booking successful', newReservation);
  });

  confirm = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;
    const updatedReservation = await Reservation.findOneAndUpdate(
      { _id: req.params.id, propertyId },
      { status: 'confirmed' },
      { new: true },
    );
    ResponseUtil.success(res, 'Reservation confirmed', updatedReservation);
  });

  release = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { propertyId } = req.params;
    const updatedReservation = await Reservation.findOneAndUpdate(
      { _id: req.params.id, propertyId },
      { status: 'cancelled' },
      { new: true },
    );
    ResponseUtil.success(res, 'Reservation released', updatedReservation);
  });

  get = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const reservation = await Reservation.findById(req.params.id).populate({
      path: 'items',
      populate: [{ path: 'unitTypeId' }, { path: 'packageId' }, { path: 'taxes' }],
    });
    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }
    ResponseUtil.success(res, 'Reservation retrieved successfully', reservation);
  });

  getByPropertyId = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const userRole = req.user?.role;

    const propertyId = req.params.propertyId;
    const { q, startDate, endDate } = req.query;

    const query: mongoose.FilterQuery<IReservation> = {};

    if (userRole && userRole === UserRoleEnum.MERCHANT) {
      const property = await PropertyModel.findById(propertyId);

      if (!property) {
        return ResponseUtil.notFound(res, 'Property not found');
      }

      query.propertyId = propertyId;
    }

    if (q && typeof q === 'string') {
      const searchConditions: mongoose.FilterQuery<IReservation>[] = [
        { reservationCode: { $regex: q, $options: 'i' } },
        { 'bookerDetails.name': { $regex: q, $options: 'i' } },
        { 'bookerDetails.email': { $regex: q, $options: 'i' } },
      ];

      if (userRole === UserRoleEnum.SUPERADMIN) {
        searchConditions.push({
          propertyId: {
            $in: await PropertyModel.find({ name: { $regex: q, $options: 'i' } }).distinct('_id'),
          },
        });
      }

      query.$or = searchConditions;
    }

    // Handle date range filtering
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate && typeof startDate === 'string') {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate && typeof endDate === 'string') {
        query.createdAt.$lte = new Date(endDate);
      }
    }

    const reservations = await Reservation.find(query)
      .populate({
        path: 'items',
        populate: [{ path: 'unitTypeId' }, { path: 'packageId' }, { path: 'taxes' }],
      })
      .sort({ updatedAt: -1 });

    ResponseUtil.success(res, 'Reservations retrieved successfully', reservations);
  });

  getByUser = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const userId = req.user?.userId;

    if (!userId) {
      return ResponseUtil.notFound(res, 'User not found');
    }

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return ResponseUtil.badRequest(res, 'Invalid user ID format');
    }

    const user = await UserSchema.findById(userId);

    if (!user) {
      return ResponseUtil.notFound(res, 'User not found');
    }

    const reservations = await Reservation.find({
      'bookerDetails.email': user.email,
    })
      .populate({
        path: 'items',
        populate: [{ path: 'unitTypeId' }, { path: 'packageId' }, { path: 'taxes' }],
      })
      .sort({ createdAt: -1 });
    ResponseUtil.success(res, 'Reservations retrieved successfully', reservations);
  });

  getRefundableAmount = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { reservationId } = req.params;
    const { cancellationTime } = req.query;

    const validationResult = await validateRefundInputs(reservationId, cancellationTime as string, res);
    if (!validationResult) return;

    const { refundPolicy, refundableAmount } = validationResult;

    ResponseUtil.success(res, 'Refundable amount retrieved successfully', {
      refundableAmount,
      refundablePercentage: refundPolicy.refund_percent || 0,
      description: refundPolicy.description,
    });
  });

  getOtp = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { reservationCode } = req.body;

    const reservation = await Reservation.findOne({ reservationCode });

    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }

    const userEmail = reservation.bookerDetails.email;

    const purpose = OtpPurposeEnum.CANCEL_BOOKING;

    const otp = generateRandomOTP(6);

    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

    const existingOTP = await OTPSchema.findOne({
      userEmail,
      reservationCode,
      purpose,
    });
    if (existingOTP) {
      existingOTP.otp = otp;
      existingOTP.expiresAt = expiresAt;
      existingOTP.createdAt = new Date();
      await existingOTP.save();
    } else {
      const newOTP: IOtp = new OTPSchema({
        userEmail,
        reservationCode,
        purpose,
        otp,
        expiresAt,
      });
      await newOTP.save();
    }

    await this.emailService.sendOtpVerificationEmail(userEmail, reservation.bookerDetails.firstName, otp);

    ResponseUtil.success(res, 'OTP generated successfully');
  });

  verifyOtp = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const { reservationCode, otp } = req.body;

    const existingOTP = await OTPSchema.findOne({
      reservationCode,
      purpose: OtpPurposeEnum.CANCEL_BOOKING,
      otp,
      expiresAt: { $gt: new Date() },
    });

    if (!existingOTP) {
      return ResponseUtil.badRequest(res, 'Invalid OTP');
    }

    const reservation = await Reservation.findOne({ reservationCode });

    if (!reservation) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }

    await existingOTP.deleteOne();

    ResponseUtil.success(res, 'OTP verified successfully', reservation);
  });

  update = asyncRequestHandler(async (req: IRequest, res: IResponse) => {
    const reservationItem = await ReservationItem.findById(req.params.id);
    if (!reservationItem) {
      return ResponseUtil.notFound(res, 'Reservation not found');
    }

    if (new Date(reservationItem.startDateTime).getTime() < Date.now()) {
      return ResponseUtil.badRequest(res, 'Cannot update past reservation');
    }

    const property = await PropertyModel.findById(reservationItem.propertyId);
    if (!property) {
      return ResponseUtil.notFound(res, 'Property not found');
    }

    if (req.body.guestDetails && Array.isArray(req.body.guestDetails)) {
      await ReservationItem.updateOne(
        { _id: req.params.id },
        { $set: { guestDetails: req.body.guestDetails } },
        { runValidators: true },
      );
    }

    const updatedReservationItem = await ReservationItem.findById(req.params.id).populate([
      { path: 'unitTypeId' },
      { path: 'packageId' },
      { path: 'propertyId' },
      { path: 'taxes' },
    ]);
    if (!updatedReservationItem) {
      return ResponseUtil.notFound(res, 'Reservation not found after update');
    }

    // await this.emailService.sendUpdateReservationEmail(
    //   updatedReservationItem.bookerDetails.email,
    //   updatedReservationItem,
    //   property,
    // );

    ResponseUtil.success(res, 'Reservation updated successfully', updatedReservationItem);
  });
}
