import { BaseSchema } from './base.model';
import mongoose, { Schema } from 'mongoose';
import { IPropertyCancellationPolicy, IPropertyCustomPolicy, IPropertyPolicies, IPropertyPolicy } from '../types';

const policySchema = new mongoose.Schema<IPropertyPolicy>(
  {
    policyId: { type: Schema.Types.ObjectId, required: true, ref: 'Policy' },
    value: { type: String, required: true },
  },
  { _id: false },
);

const cancellationPolicySchema = new mongoose.Schema<IPropertyCancellationPolicy>(
  {
    hours: { type: Number, required: true },
    refund_percent: { type: Number, required: true },
    description: { type: String, required: true },
  },
  { _id: false },
);

const customPolicySchema = new mongoose.Schema<IPropertyCustomPolicy>(
  {
    title: { type: String, required: true },
    description: { type: String, required: true },
  },
  { _id: false },
);

export const PropertyPoliciesSchema = new mongoose.Schema<IPropertyPolicies>({
  propertyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Property',
    required: true,
  },
  policies: [policySchema],
  cancellationPolicies: [cancellationPolicySchema],
  customPolicies: [customPolicySchema],
});

PropertyPoliciesSchema.add(BaseSchema);

export default mongoose.model<IPropertyPolicies>('PropertyPolicies', PropertyPoliciesSchema, 'PropertyPolicies');
