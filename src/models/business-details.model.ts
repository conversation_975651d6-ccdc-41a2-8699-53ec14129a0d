import mongoose from 'mongoose';
import { BaseSchema } from './base.model';
import { IBusinessDetails, IPersonalIdProof, ISalesTax, MonthsEnum } from '../types';

const personalIdProofSchema = new mongoose.Schema<IPersonalIdProof>(
  {
    idType: { type: String, required: true },
    idProof: { type: [String], required: true },
    idName: { type: String, required: true },
  },
  { _id: false },
);

const salesTaxSchema = new mongoose.Schema<ISalesTax>(
  {
    name: { type: mongoose.Schema.Types.ObjectId, required: true, ref: 'DomainValue' },
    taxPercentage: { type: Number, required: true },
  },
  { _id: false },
);

const commissionSchema = new mongoose.Schema(
  {
    percentage: { type: Number, required: true },
    frequency: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'DomainValue',
      required: true,
    },
  },
  { _id: false },
);

const BusinessDetailsSchema = new mongoose.Schema<IBusinessDetails>({
  propertyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Property',
    required: true,
  },
  registrationNumber: { type: String, required: true },
  businessTaxId: { type: String, required: true },
  financialYearStart: { type: String, enum: Object.values(MonthsEnum), required: true },
  financialYearEnd: { type: String, enum: Object.values(MonthsEnum), required: true },
  registrationDocuments: { type: [String], required: true },
  userPersonalId: personalIdProofSchema,
  salesTax: [salesTaxSchema],
  commission: commissionSchema,
  serviceCharges: {
    type: [mongoose.Schema.Types.ObjectId],
    ref: 'DomainValue',
    required: true,
  },
});

BusinessDetailsSchema.index({ propertyId: 1 }, { unique: true });

BusinessDetailsSchema.add(BaseSchema);

export default mongoose.model<IBusinessDetails>('BusinessDetails', BusinessDetailsSchema, 'BusinessDetails');
