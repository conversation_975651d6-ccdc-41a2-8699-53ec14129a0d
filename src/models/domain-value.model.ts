import mongoose, { Schema } from 'mongoose';
import { BaseSchema } from './base.model';
import { DomainValueLevel, IDomainValue } from '../types';

const DomainValueSchema: Schema = new Schema<IDomainValue>({
  propertyId: {
    type: Schema.Types.ObjectId,
    required: [
      function (this: IDomainValue) {
        return [DomainValueLevel.PROPERTY_DATA].includes(this.level as DomainValueLevel);
      },
      'propertyId is required for PROPERTY_DATA levels',
    ],
    ref: 'Property',
  },
  category: { type: String, required: false },
  level: { type: String, required: true, enum: Object.values(DomainValueLevel) },
  code: { type: String, required: true },
  name: { type: String, required: true },
  description: { type: String, required: false },
  value: { type: String, required: false, default: '' },
  icon: { type: String, required: false, default: '' },
  displayOrder: { type: Number, required: false },
  categoryId: {
    type: Schema.Types.ObjectId,
    required: false,
    ref: 'DomainValue',
  },
  parentId: { type: Schema.Types.ObjectId, required: false, ref: 'DomainValue' },
});

DomainValueSchema.add(BaseSchema);

DomainValueSchema.index(
  { propertyId: 1, categoryId: 1, level: 1, parentId: 1, name: 1 },
  {
    unique: true,
    partialFilterExpression: {
      propertyId: { $exists: true },
    },
  },
);

export default mongoose.model<IDomainValue>('DomainValue', DomainValueSchema, 'DomainValue');
