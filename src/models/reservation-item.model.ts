import mongoose, { Schema } from 'mongoose';
import { addressSchema, phoneSchema } from './property.model';
import { BaseSchema } from './base.model';
import { GenderEnum, IReservationItem, PaymentStatusEnum, ReservationStatusEnum } from '../types';

const ReservationItemSchema: Schema = new Schema<IReservationItem>({
  reservationCode: { type: String, required: true },
  propertyId: {
    type: Schema.Types.ObjectId,
    ref: 'Property',
    required: true,
  },
  // Flattened reservation details (previously nested in reservations array)
  couponDiscount: { type: Number, required: true, default: 0 },
  unitTypeId: {
    type: Schema.Types.ObjectId,
    ref: 'RoomType',
    required: true,
  },
  packageId: {
    type: Schema.Types.ObjectId,
    ref: 'Package',
    required: true,
  },
  startDateTime: { type: Date, required: true },
  endDateTime: { type: Date, required: true },
  noOfAdults: { type: Number, required: true, default: 1 },
  noOfChildren: { type: Number, required: true, default: 0 },
  price: { type: Number, required: true },
  taxes: {
    type: [{ type: Schema.Types.ObjectId, ref: 'DomainValue' }],
    required: false,
  },
  tax: { type: Number, required: true },
  totalAmount: { type: Number, required: true },
  guestDetails: [
    {
      firstName: { type: String, required: true },
      lastName: { type: String, required: true },
      email: { type: String, required: false },
      phone: phoneSchema,
      address: addressSchema,
      gender: {
        type: String,
        enum: Object.values(GenderEnum),
        required: true,
        default: GenderEnum.OTHER,
      },
    },
  ],
  specialRequest: { type: String, required: false, default: '' },
  flightDetails: {
    number: { type: String, required: true },
    from: { type: String, required: true },
    to: { type: String, required: true },
    arrivalDateTime: { type: Date, required: false },
    departureDateTime: { type: Date, required: false },
  },
  bookerDetails: {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    address: addressSchema,
    email: { type: String, required: true },
    phone: phoneSchema,
    gender: {
      type: String,
      enum: Object.values(GenderEnum),
      required: true,
      default: GenderEnum.OTHER,
    },
  },
  status: {
    type: String,
    enum: Object.values(ReservationStatusEnum),
    default: ReservationStatusEnum.PENDING,
    required: true,
  },
  paymentStatus: {
    type: String,
    enum: Object.values(PaymentStatusEnum),
    default: PaymentStatusEnum.PENDING,
    required: true,
  },
  refundAmount: { type: Number, required: false },
  cancellationReason: { type: String, required: false },
});

ReservationItemSchema.add(BaseSchema);

ReservationItemSchema.index({ reservationCode: 1, propertyId: 1 }, { unique: true });
ReservationItemSchema.index({ propertyId: 1, startDateTime: 1, endDateTime: 1 });

export default mongoose.model<IReservationItem>('ReservationItem', ReservationItemSchema, 'ReservationItem');
