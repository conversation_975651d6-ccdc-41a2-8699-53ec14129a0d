import mongoose from 'mongoose';
import { BaseSchema } from './base.model';
import { IPropertyDetails } from '../types';

const PropertyDetailsSchema = new mongoose.Schema<IPropertyDetails>({
  propertyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Property',
    required: true,
  },
  isPropertyChain: { type: Boolean, required: true, default: false },
  totalUnits: { type: Number, required: true, default: 0 },
  noOfReservationsPerPerson: { type: Number, required: true, default: 0 },
  minReservationLength: { type: Number, required: true, default: 0 },
  maxReservationLength: { type: Number, required: true, default: 0 },
  bookingIdPrefix: { type: String, required: true },
  invoiceIdPrefix: { type: String, required: true },
  frontDeskOpeningTime: { type: String, required: true },
  isOpen24Hours: { type: Boolean, required: true, default: false },
  aboutProperty: { type: String, required: true },
  businessLogo: { type: String, required: true },
  isAllUnitsInSameAddress: { type: Boolean, required: true, default: false },
  accommodationType: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DomainValue',
    required: false,
  },
  currency: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DomainValue',
    required: true,
  },
  mailEvents: {
    type: [mongoose.Schema.Types.ObjectId],
    ref: 'DomainValue',
    required: true,
  },
  amenities: {
    type: [mongoose.Schema.Types.ObjectId],
    ref: 'Amenities',
    required: true,
    default: [],
  },
  frontDeskClosingTime: { type: String, required: true, default: '' },
  sharedUnits: { type: Number, required: true, default: 0 },
  privateUnits: { type: Number, required: true, default: 0 },
  isForExclusiveUse: { type: Boolean, required: true, default: false },
});

PropertyDetailsSchema.index({ propertyId: 1 }, { unique: true });

PropertyDetailsSchema.add(BaseSchema);

export default mongoose.model<IPropertyDetails>('PropertyDetails', PropertyDetailsSchema, 'PropertyDetails');
