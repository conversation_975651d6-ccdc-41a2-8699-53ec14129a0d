import mongoose, { Schema } from 'mongoose';
import Sequence from './sequence.model';
import { BaseSchema } from './base.model';
import { GenderEnum, IReservation, PaymentStatusEnum, ReservationStatusEnum } from '../types';
import { addressSchema, phoneSchema } from './property.model';
import PropertyDetailsModel from './property-details.model';

const ReservationSchema: Schema = new Schema<IReservation>({
  reservationCode: { type: String, required: true },
  invoiceCode: { type: String, required: true },
  propertyId: {
    type: Schema.Types.ObjectId,
    ref: 'Property',
    required: true,
  },
  items: [
    {
      type: Schema.Types.ObjectId,
      ref: 'ReservationItem',
    },
  ],
  subTotal: { type: Number, required: true },
  totalTax: { type: Number, required: true },
  grandTotal: { type: Number, required: true },
  status: {
    type: String,
    enum: Object.values(ReservationStatusEnum),
    default: ReservationStatusEnum.PENDING,
    required: true,
  },
  paymentStatus: {
    type: String,
    enum: Object.values(PaymentStatusEnum),
    default: PaymentStatusEnum.PENDING,
    required: true,
  },
  bookerDetails: {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    address: addressSchema,
    email: { type: String, required: true },
    phone: phoneSchema,
    gender: {
      type: String,
      enum: Object.values(GenderEnum),
      required: true,
      default: GenderEnum.OTHER,
    },
  },
  refundAmount: { type: Number, required: false },
  cancellationReason: { type: String, required: false },
});

ReservationSchema.pre<IReservation>('save', async function (next) {
  try {
    if (this.isNew) {
      const reservationCodeSequence = await Sequence.findOneAndUpdate(
        { name: 'reservationCode' },
        { $inc: { sequence_value: 1 } },
        { new: true, upsert: true, setDefaultsOnInsert: true },
      );
      const invoiceCodeSequence = await Sequence.findOneAndUpdate(
        { name: 'invoiceCode' },
        { $inc: { sequence_value: 1 } },
        { new: true, upsert: true, setDefaultsOnInsert: true },
      );
      const property = await PropertyDetailsModel.findOne({ propertyId: this.propertyId });
      if (!property) {
        throw new Error('Property not found');
      }
      if (!reservationCodeSequence) {
        throw new Error('Failed to generate sequence number');
      }
      this.reservationCode = `${property.bookingIdPrefix}-${new Date().getFullYear().toString().slice(-2)}${(new Date().getMonth() + 1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}-${reservationCodeSequence.sequence_value.toString().padStart(6, '0')}`;
      if (!invoiceCodeSequence) {
        throw new Error('Failed to generate sequence number');
      }
      this.invoiceCode = `${property.invoiceIdPrefix}-${new Date().getFullYear().toString().slice(-2)}${(new Date().getMonth() + 1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}-${invoiceCodeSequence.sequence_value.toString().padStart(6, '0')}`;
    }
    next();
  } catch (err) {
    next(err instanceof Error ? err : new Error(String(err)));
  }
});

ReservationSchema.add(BaseSchema);

ReservationSchema.index({ reservationCode: 1 }, { unique: true });
ReservationSchema.index({ invoiceCode: 1 }, { unique: true });

export default mongoose.model<IReservation>('Reservation', ReservationSchema, 'Reservation');
